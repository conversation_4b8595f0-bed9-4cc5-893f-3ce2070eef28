#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多Agent路径规划性能测试
专注于性能测试，不进行可视化以避免matplotlib多线程问题
"""

import sys
import os
import time
import random
import numpy as np

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 使用与单路径测试相同的模拟地图
class MockMap3D:
    def __init__(self):
        self.height = 1000
        self.width = 1000
        self.depth = 100

        # 模拟障碍物管理器
        self.obstacle_manager = MockObstacleManager()

        # 模拟网格转换器
        self.grid_converter = MockGridConverter()

        # 模拟不可通行点集合 - 创建几个禁飞区
        self.non_traversable = set()

        # 禁飞区1: 圆形
        center1 = (300, 400)
        radius1 = 50
        for y in range(center1[0] - radius1, center1[0] + radius1):
            for x in range(center1[1] - radius1, center1[1] + radius1):
                if (y - center1[0]) ** 2 + (x - center1[1]) ** 2 <= radius1**2:
                    for z in range(0, 80):
                        self.non_traversable.add((y, x, z))

        # 禁飞区2: 矩形
        for y in range(600, 700):
            for x in range(300, 450):
                for z in range(0, 80):
                    self.non_traversable.add((y, x, z))

        # 禁飞区3: 圆形
        center3 = (650, 450)
        radius3 = 40
        for y in range(center3[0] - radius3, center3[0] + radius3):
            for x in range(center3[1] - radius3, center3[1] + radius3):
                if (y - center3[0]) ** 2 + (x - center3[1]) ** 2 <= radius3**2:
                    for z in range(0, 80):
                        self.non_traversable.add((y, x, z))

    def traversable(self, y, x, z):
        return (y, x, z) not in self.non_traversable


class MockObstacleManager:
    def __init__(self):
        self.orbit_paths = {}
        self.orbit_quadrants = {}
        self.orbit_points_map = {}

        # 创建禁飞区1的轨道 (圆形)
        center1 = (300, 400)
        radius1 = 51
        points1 = 64  # 固定点数以提高性能
        orbit1 = []
        for i in range(points1):
            angle = 2 * np.pi * i / points1
            y = int(center1[0] + radius1 * np.cos(angle))
            x = int(center1[1] + radius1 * np.sin(angle))
            orbit1.append((y, x))

        self.orbit_paths["zone1"] = orbit1
        self.orbit_points_map["zone1"] = {(p[0], p[1]): i for i, p in enumerate(orbit1)}
        
        # 创建象限信息
        self._create_quadrants("zone1", orbit1, center1)

        # 创建禁飞区2的轨道 (矩形)
        orbit2 = []
        # 上边
        for x in range(299, 452):
            orbit2.append((701, x))
        # 右边
        for y in range(701, 598, -1):
            orbit2.append((y, 451))
        # 下边
        for x in range(451, 298, -1):
            orbit2.append((599, x))
        # 左边
        for y in range(599, 702):
            orbit2.append((y, 299))

        self.orbit_paths["zone2"] = orbit2
        self.orbit_points_map["zone2"] = {(p[0], p[1]): i for i, p in enumerate(orbit2)}
        
        center2 = (650, 375)  # 矩形中心
        self._create_quadrants("zone2", orbit2, center2)

        # 创建禁飞区3的轨道 (圆形)
        center3 = (650, 450)
        radius3 = 41
        points3 = 64  # 固定点数以提高性能
        orbit3 = []
        for i in range(points3):
            angle = 2 * np.pi * i / points3
            y = int(center3[0] + radius3 * np.cos(angle))
            x = int(center3[1] + radius3 * np.sin(angle))
            orbit3.append((y, x))

        self.orbit_paths["zone3"] = orbit3
        self.orbit_points_map["zone3"] = {(p[0], p[1]): i for i, p in enumerate(orbit3)}
        
        self._create_quadrants("zone3", orbit3, center3)

    def _create_quadrants(self, zone_name, orbit_points, center):
        """创建象限信息"""
        quadrants = {
            "N": set(), "NE": set(), "E": set(), "SE": set(),
            "S": set(), "SW": set(), "W": set(), "NW": set(),
        }

        for i, (grid_y, grid_x) in enumerate(orbit_points):
            dy = grid_y - center[0]
            dx = grid_x - center[1]

            angle_rad = np.arctan2(dx, dy)
            angle_deg = np.degrees(angle_rad)
            if angle_deg < 0:
                angle_deg += 360

            if 337.5 <= angle_deg or angle_deg < 22.5:
                quadrants["E"].add(i)
            elif 22.5 <= angle_deg < 67.5:
                quadrants["NE"].add(i)
            elif 67.5 <= angle_deg < 112.5:
                quadrants["N"].add(i)
            elif 112.5 <= angle_deg < 157.5:
                quadrants["NW"].add(i)
            elif 157.5 <= angle_deg < 202.5:
                quadrants["W"].add(i)
            elif 202.5 <= angle_deg < 247.5:
                quadrants["SW"].add(i)
            elif 247.5 <= angle_deg < 292.5:
                quadrants["S"].add(i)
            elif 292.5 <= angle_deg < 337.5:
                quadrants["SE"].add(i)

        self.orbit_quadrants[zone_name] = quadrants

    def get_type_at_position(self, pos):
        y, x, z = pos

        # 检查禁飞区1 (圆形)
        center1 = (300, 400)
        if (y - center1[0]) ** 2 + (x - center1[1]) ** 2 <= 50**2:
            return ["zone1"]

        # 检查禁飞区2 (矩形)
        if 600 <= y < 700 and 300 <= x < 450:
            return ["zone2"]

        # 检查禁飞区3 (圆形)
        center3 = (650, 450)
        if (y - center3[0]) ** 2 + (x - center3[1]) ** 2 <= 40**2:
            return ["zone3"]

        return []

    def get_orbit_path(self, zone_name):
        return self.orbit_paths.get(zone_name, [])

    def get_orbit_quadrants(self, zone_name):
        return self.orbit_quadrants.get(zone_name, {})


class MockGridConverter:
    def relative_to_geo(self, y, x, z):
        return {"lat": 39.0 + y * 0.001, "lon": 116.0 + x * 0.001, "alt": z * 10}


def generate_random_tasks(num_tasks=100, map_size=(1000, 1000), min_height=50):
    """生成随机的路径规划任务"""
    tasks = []
    
    # 避开禁飞区的安全区域
    safe_zones = [
        (0, 250, 0, 350),      # 左上角
        (0, 250, 500, 1000),   # 左下角
        (750, 1000, 0, 1000),  # 右侧
        (250, 550, 0, 250),    # 中上
        (250, 550, 500, 1000), # 中下
    ]
    
    for i in range(num_tasks):
        # 随机选择起点和终点的安全区域
        start_zone = random.choice(safe_zones)
        goal_zone = random.choice(safe_zones)
        
        # 确保起点和终点不在同一区域
        while goal_zone == start_zone:
            goal_zone = random.choice(safe_zones)
        
        start = (
            random.randint(start_zone[0], start_zone[1]),
            random.randint(start_zone[2], start_zone[3]),
            random.randint(0, 30)  # 起始高度较低
        )
        
        goal = (
            random.randint(goal_zone[0], goal_zone[1]),
            random.randint(goal_zone[2], goal_zone[3]),
            random.randint(0, 30)   # 目标高度较低
        )
        
        task = {
            'agent_id': f'drone_{i:03d}',
            'start': start,
            'goal': goal,
            'min_height': min_height
        }
        tasks.append(task)
    
    return tasks


def test_performance():
    """性能测试"""
    print("=" * 80)
    print("多Agent路径规划性能测试")
    print("=" * 80)
    
    # 导入多Agent规划器
    from src.core.pathfinding.multi_agent_planner import MultiAgentPlanner
    
    # 创建模拟地图
    mock_map = MockMap3D()
    
    # 测试不同的工作线程数
    worker_counts = [4, 8, 12, 16]
    test_scales = [100, 500, 1000]
    
    results_table = []
    
    for num_workers in worker_counts:
        print(f"\n使用 {num_workers} 个工作线程:")
        print("-" * 60)
        
        # 创建多Agent规划器
        planner = MultiAgentPlanner(mock_map, num_workers=num_workers)
        
        for num_tasks in test_scales:
            print(f"  测试规模: {num_tasks} 条路径", end=" ... ")
            
            # 生成随机任务
            tasks = generate_random_tasks(num_tasks)
            
            # 执行路径规划
            start_time = time.time()
            results = planner.plan_paths(tasks)
            total_time = time.time() - start_time
            
            # 获取统计信息
            stats = planner.get_statistics(results)
            
            throughput = num_tasks / total_time
            success_rate = stats.get('success_rate', 0)
            avg_time = stats.get('average_planning_time', 0)
            
            print(f"完成")
            print(f"    总耗时: {total_time:.3f}s")
            print(f"    成功率: {success_rate:.1f}%")
            print(f"    平均规划时间: {avg_time:.4f}s")
            print(f"    吞吐量: {throughput:.1f} 路径/秒")
            
            results_table.append({
                'workers': num_workers,
                'tasks': num_tasks,
                'total_time': total_time,
                'success_rate': success_rate,
                'throughput': throughput,
                'avg_time': avg_time
            })
        
        # 关闭规划器
        planner.shutdown()
    
    # 打印汇总表格
    print("\n" + "=" * 80)
    print("性能汇总表")
    print("=" * 80)
    print(f"{'工作线程':<8} {'任务数':<8} {'总耗时(s)':<10} {'成功率(%)':<10} {'吞吐量(路径/s)':<15} {'平均时间(s)':<12}")
    print("-" * 80)
    
    for result in results_table:
        print(f"{result['workers']:<8} {result['tasks']:<8} {result['total_time']:<10.3f} "
              f"{result['success_rate']:<10.1f} {result['throughput']:<15.1f} {result['avg_time']:<12.4f}")
    
    # 找出最佳配置
    best_1000 = max([r for r in results_table if r['tasks'] == 1000], 
                    key=lambda x: x['throughput'], default=None)
    
    if best_1000:
        print(f"\n1000条路径的最佳配置:")
        print(f"  工作线程数: {best_1000['workers']}")
        print(f"  吞吐量: {best_1000['throughput']:.1f} 路径/秒")
        print(f"  总耗时: {best_1000['total_time']:.3f}s")
        print(f"  是否达到目标(1000路径/1秒): {'是' if best_1000['total_time'] <= 1.0 else '否'}")


if __name__ == "__main__":
    test_performance()
