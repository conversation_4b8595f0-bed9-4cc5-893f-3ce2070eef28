# -*- coding: utf-8 -*-
"""
多Agent并行路径规划器
核心功能：
1. 高性能并行批量路径规划
2. 智能负载均衡和任务分配
3. 基于轨道算法的路径规划
4. 支持1000条路径在1秒内完成
"""
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from multiprocessing import cpu_count
from typing import Dict, List, Tuple, Optional, Set, Union
from dataclasses import dataclass
from ..node_3d import GridNode3D
from ...utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class PlanningTask:
    """路径规划任务"""

    agent_id: str
    start: Tuple[int, int, int]
    goal: Tuple[int, int, int]
    min_height: int = 50
    start_time: Optional[int] = None


@dataclass
class PlanningResult:
    """路径规划结果"""

    agent_id: str
    path: Optional[List[GridNode3D]]
    error: Optional[str]
    planning_time: float
    success: bool


class ThreadSafeMapWrapper:
    """线程安全的地图包装器"""

    def __init__(self, map3d):
        """
        初始化线程安全的地图包装器
        Args:
            map3d: 原始Map3D实例
        """
        self.map3d = map3d
        self._lock = threading.RLock()

    def get_map_reference(self):
        """获取地图引用（线程安全）"""
        with self._lock:
            return self.map3d


class HighPerformanceWorkerPool:
    """高性能并行工作池"""

    def __init__(
        self, map_wrapper: ThreadSafeMapWrapper, num_workers: Optional[int] = None
    ):
        """
        初始化工作池
        Args:
            map_wrapper: 线程安全的地图包装器
            num_workers: 工作线程数，默认为CPU核心数
        """
        self.map_wrapper = map_wrapper
        self.num_workers = num_workers or min(cpu_count(), 16)  # 限制最大线程数
        self.executor = ThreadPoolExecutor(max_workers=self.num_workers)

        logger.info(f"初始化工作池，使用 {self.num_workers} 个工作线程")

    def process_tasks_parallel(self, tasks: List[PlanningTask]) -> List[PlanningResult]:
        """
        并行处理路径规划任务
        Args:
            tasks: 路径规划任务列表
        Returns:
            路径规划结果列表
        """
        if not tasks:
            return []

        # 将任务分组以优化负载均衡
        task_groups = self._distribute_tasks(tasks)

        # 提交所有任务组
        futures = []
        for task_group in task_groups:
            future = self.executor.submit(self._process_task_group, task_group)
            futures.append(future)

        # 收集结果
        results = []
        for future in as_completed(futures):
            try:
                group_results = future.result()
                results.extend(group_results)
            except Exception as e:
                logger.error(f"任务组处理失败: {e}")
                # 为失败的任务创建错误结果
                results.append(
                    PlanningResult(
                        agent_id="unknown",
                        path=None,
                        error=str(e),
                        planning_time=0.0,
                        success=False,
                    )
                )

        return results

    def _distribute_tasks(self, tasks: List[PlanningTask]) -> List[List[PlanningTask]]:
        """
        智能分配任务到工作线程
        Args:
            tasks: 任务列表
        Returns:
            分组后的任务列表
        """
        if len(tasks) <= self.num_workers:
            # 任务数少于工作线程数，每个任务一个线程
            return [[task] for task in tasks]

        # 计算每组的任务数
        tasks_per_group = len(tasks) // self.num_workers
        remainder = len(tasks) % self.num_workers

        task_groups = []
        start_idx = 0

        for i in range(self.num_workers):
            # 前remainder个组多分配一个任务
            group_size = tasks_per_group + (1 if i < remainder else 0)
            end_idx = start_idx + group_size

            if start_idx < len(tasks):
                task_groups.append(tasks[start_idx:end_idx])

            start_idx = end_idx

        return task_groups

    def _process_task_group(
        self, task_group: List[PlanningTask]
    ) -> List[PlanningResult]:
        """
        处理一组任务
        Args:
            task_group: 任务组
        Returns:
            结果列表
        """
        from .orbit import OrbitPathFinder  # 延迟导入避免循环依赖

        results = []
        map_ref = self.map_wrapper.get_map_reference()

        # 为这个线程创建一个OrbitPathFinder实例
        path_finder = OrbitPathFinder(map_ref)

        for task in task_group:
            start_time = time.time()

            try:
                # 执行路径规划
                path, error = path_finder.find_path(
                    start=task.start,
                    goal=task.goal,
                    min_height=task.min_height,
                    agent_id=task.agent_id,
                    start_time=task.start_time,
                    occupancy_map=None,  # 暂时不使用占用图避免冲突
                    constraints=None,
                )

                planning_time = time.time() - start_time

                result = PlanningResult(
                    agent_id=task.agent_id,
                    path=path,
                    error=error,
                    planning_time=planning_time,
                    success=path is not None,
                )

            except Exception as e:
                planning_time = time.time() - start_time
                logger.error(f"任务 {task.agent_id} 处理失败: {e}")

                result = PlanningResult(
                    agent_id=task.agent_id,
                    path=None,
                    error=str(e),
                    planning_time=planning_time,
                    success=False,
                )

            results.append(result)

        return results

    def shutdown(self):
        """关闭工作池"""
        self.executor.shutdown(wait=True)


class MultiAgentPlanner:
    """高性能多Agent路径规划器"""

    def __init__(self, map3d, num_workers: Optional[int] = None):
        """
        初始化多Agent路径规划器
        Args:
            map3d: 3D地图实例
            num_workers: 工作线程数，默认为CPU核心数
        """
        self.map_wrapper = ThreadSafeMapWrapper(map3d)  # 提供线程安全的地图访问
        self.worker_pool = HighPerformanceWorkerPool(
            self.map_wrapper, num_workers
        )  # 管理并行任务执行的工作线程池

        logger.info("多Agent路径规划器初始化完成")

    def plan_paths(
        self, tasks: List[Union[PlanningTask, Dict]], timeout: float = 60.0
    ) -> Dict[str, PlanningResult]:
        """
        批量规划路径
        Args:
            tasks: 路径规划任务列表，可以是PlanningTask对象或字典
            timeout: 超时时间（秒）
        Returns:
            {agent_id: PlanningResult} 规划结果字典
        """
        start_time = time.time()

        # 标准化任务格式
        planning_tasks = self._normalize_tasks(tasks)

        if not planning_tasks:
            logger.warning("没有有效的规划任务")
            return {}

        logger.info(f"开始规划 {len(planning_tasks)} 条路径")

        try:
            # 并行处理所有任务
            results = self.worker_pool.process_tasks_parallel(planning_tasks)

            # 转换为字典格式
            result_dict = {result.agent_id: result for result in results}

            # 统计结果
            total_time = time.time() - start_time
            success_count = sum(1 for r in results if r.success)

            logger.info(
                f"路径规划完成: {success_count}/{len(results)} 成功, "
                f"总耗时: {total_time:.3f}s, "
                f"平均耗时: {total_time/len(results):.3f}s/路径"
            )

            return result_dict

        except Exception as e:
            logger.error(f"批量路径规划失败: {e}")
            # 返回失败结果
            return {
                task.agent_id: PlanningResult(
                    agent_id=task.agent_id,
                    path=None,
                    error=str(e),
                    planning_time=0.0,
                    success=False,
                )
                for task in planning_tasks
            }

    def _normalize_tasks(
        self, tasks: List[Union[PlanningTask, Dict]]
    ) -> List[PlanningTask]:
        """
        标准化任务格式
        Args:
            tasks: 原始任务列表
        Returns:
            标准化的PlanningTask列表
        """
        planning_tasks = []

        for task in tasks:
            if isinstance(task, PlanningTask):
                planning_tasks.append(task)
            elif isinstance(task, dict):
                # 从字典创建PlanningTask
                planning_task = PlanningTask(
                    agent_id=task.get("agent_id", f"agent_{len(planning_tasks)}"),
                    start=task["start"],
                    goal=task["goal"],
                    min_height=task.get("min_height", 50),
                    start_time=task.get("start_time", None),
                )
                planning_tasks.append(planning_task)
            elif isinstance(task, (list, tuple)) and len(task) >= 3:
                # 从元组创建PlanningTask (agent_id, start, goal, ...)
                planning_task = PlanningTask(
                    agent_id=task[0],
                    start=task[1],
                    goal=task[2],
                    min_height=task[3] if len(task) > 3 else 50,
                    start_time=task[4] if len(task) > 4 else None,
                )
                planning_tasks.append(planning_task)
            else:
                logger.warning(f"无效的任务格式: {task}")

        return planning_tasks

    def plan_single_path(
        self,
        agent_id: str,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        min_height: int = 50,
        start_time: Optional[int] = None,
    ) -> PlanningResult:
        """
        规划单条路径
        Args:
            agent_id: 智能体ID
            start: 起点坐标
            goal: 终点坐标
            min_height: 最小巡航高度
            start_time: 起始时间
        Returns:
            路径规划结果
        """
        task = PlanningTask(
            agent_id=agent_id,
            start=start,
            goal=goal,
            min_height=min_height,
            start_time=start_time,
        )

        results = self.plan_paths([task])
        return results.get(
            agent_id,
            PlanningResult(
                agent_id=agent_id,
                path=None,
                error="规划失败",
                planning_time=0.0,
                success=False,
            ),
        )

    def get_statistics(self, results: Dict[str, PlanningResult]) -> Dict:
        """
        获取规划统计信息
        Args:
            results: 规划结果字典
        Returns:
            统计信息字典
        """
        if not results:
            return {}

        success_results = [r for r in results.values() if r.success]
        failed_results = [r for r in results.values() if not r.success]

        planning_times = [r.planning_time for r in results.values()]

        stats = {
            "total_tasks": len(results),
            "successful_tasks": len(success_results),
            "failed_tasks": len(failed_results),
            "success_rate": len(success_results) / len(results) * 100,
            "total_planning_time": sum(planning_times),
            "average_planning_time": sum(planning_times) / len(planning_times),
            "min_planning_time": min(planning_times),
            "max_planning_time": max(planning_times),
        }

        if success_results:
            success_times = [r.planning_time for r in success_results]
            stats.update(
                {
                    "average_success_time": sum(success_times) / len(success_times),
                    "min_success_time": min(success_times),
                    "max_success_time": max(success_times),
                }
            )

        return stats

    def shutdown(self):
        """关闭规划器"""
        self.worker_pool.shutdown()
        logger.info("多Agent路径规划器已关闭")
