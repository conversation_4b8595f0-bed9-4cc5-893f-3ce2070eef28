import json
import os
import sys
from dataclasses import dataclass, field
from typing import List, Optional, Dict
from pathlib import Path
from ..utils.logging import get_logger

# import paho.mqtt.client as mqtt # Keep if MQTT client logic remains elsewhere, remove if unused

logger = get_logger(__name__)


@dataclass
class DatabaseConfig:
    """数据库配置"""

    host: str
    port: int
    database: str
    user: str
    password: str
    use_unicode: bool
    get_warnings: bool
    charset: str


# RouteDBConfig class removed


@dataclass
class KafkaConfig:
    """Kafka配置"""

    bootstrap_servers: List[str]
    request_topic: str
    response_topic: str
    response_topic_sora: str
    uav_topic: str
    uav_turning_topic: str
    no_fly_zone_and_flight_status: str
    flighttask_state_topic: str
    flighttask_method_topic: str
    group_id: str


@dataclass
class GridSizeConfig:
    """网格尺寸配置"""

    lat: float
    lon: float
    alt: float


@dataclass
class MinCoordsConfig:
    """最小坐标配置"""

    lat: float
    lon: float
    alt: float


@dataclass
class MapConfig:
    """地图配置"""

    # Static parts
    boundary_thickness: int
    buffer_distance_meters: int
    downsample_ratio: float
    # Dynamic parts (will be loaded based on db_name)
    height: int
    width: int
    depth: int
    min_cruise_alt: int
    max_cruise_alt: int
    # Added from grid_mappings.json
    precision: int
    grid_size: GridSizeConfig
    min_coords: MinCoordsConfig


@dataclass
class MQTTConfig:
    """MQTT配置"""

    enabled: bool
    broker: str
    username: str
    password: str
    device_code: str
    workspace: str


@dataclass
class RedisConfig:
    """Redis配置"""

    host: str
    port: int
    db: int


@dataclass
class PathPlanningConfig:
    """路径规划相关配置"""

    takeoff_speed: float
    cruise_speed: float
    landing_speed: float
    max_steps: int
    safety_offsets: int
    time_buffer: int
    only_turning_points: bool
    need_smooth: bool
    smoothness: float


@dataclass
class ParallelProcessingConfig:
    """并行处理相关配置"""

    enabled: bool
    max_workers: int
    parallel_threshold: int
    iou_threshold: float
    path_margin: float
    height_diff_threshold: float


class Settings:
    """配置管理类"""

    def __init__(
        self, config_path: Path = None, location: str = None, server_address: str = None
    ):
        """
        初始化配置

        Args:
            config_path: 可选的配置文件路径
            location: 可选的地点参数，优先级高于配置文件
            server_address: 可选的服务器地址参数，优先级高于配置文件
        """
        # 尝试多个可能的配置文件位置
        possible_paths = [
            Path("config.json"),  # 当前工作目录
            Path(
                os.path.join(
                    os.path.dirname(os.path.abspath(sys.argv[0])), "config.json"
                )
            ),  # 可执行文件所在目录
            Path(
                os.path.join(
                    os.path.dirname(os.path.abspath(__file__)), "../../config.json"
                )
            ),  # 项目根目录
        ]

        # 如果提供了特定路径，优先使用
        if config_path:
            possible_paths.insert(0, config_path)

        # 尝试每个可能的路径
        config_loaded = False
        for path in possible_paths:
            try:
                logger.info(f"尝试从 {path} 加载配置")
                with open(path, "r", encoding="utf-8") as f:
                    raw_config_data = json.load(f)
                logger.info(f"成功从 {path} 加载配置")
                config_path = path  # 更新配置路径为成功加载的路径
                config_loaded = True
                break
            except (FileNotFoundError, json.JSONDecodeError) as e:
                logger.warning(f"无法从 {path} 加载配置: {str(e)}")
                continue

        if not config_loaded:
            raise FileNotFoundError(
                f"无法找到配置文件。尝试了以下路径: {', '.join(str(p) for p in possible_paths)}"
            )

        try:
            # 验证配置文件格式
            if not isinstance(raw_config_data, dict):
                raise ValueError(f"配置文件格式错误: {config_path} (应为JSON对象)")
        except json.JSONDecodeError:
            raise ValueError(f"配置文件格式错误: {config_path}")

        # --- 处理地点参数 ---
        # 如果提供了命令行参数，优先使用命令行参数
        if location:
            self.location = location
            logger.info(f"使用命令行参数指定的地点: {self.location}")
        else:
            # 否则使用配置文件中的值
            self.location = raw_config_data.get("location")
            if not self.location:
                raise ValueError(f"在 {config_path} 中必须设置顶层 'location' 字段")
            logger.info(f"使用配置文件中的地点: {self.location}")

        # --- 处理服务器地址参数 ---
        # 如果提供了命令行参数，优先使用命令行参数
        if server_address:
            self.server_address = server_address
            logger.info(f"使用命令行参数指定的服务器地址: {self.server_address}")
        else:
            # 否则使用配置文件中的值
            self.server_address = raw_config_data.get("server_address")
            if not self.server_address:
                raise ValueError(
                    f"在 {config_path} 中必须设置顶层 'server_address' 字段"
                )
            logger.info(f"使用配置文件中的服务器地址: {self.server_address}")

        # --- Placeholder Substitution Logic ---
        def substitute_placeholders(data):
            if isinstance(data, dict):
                return {k: substitute_placeholders(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [substitute_placeholders(item) for item in data]
            elif isinstance(data, str):
                return data.replace("{server_address}", self.server_address)
            else:
                return data

        config_data = substitute_placeholders(raw_config_data)
        # --- End Placeholder Substitution ---

        # Helper to get nested config or raise error (operates on substituted data)
        def get_config(key_path: str):
            keys = key_path.split(".")
            value = config_data  # Use the processed config_data
            try:
                for key in keys:
                    value = value[key]
                return value
            except KeyError:
                raise KeyError(f"在 {config_path} 中未找到配置项: {key_path}")
            except TypeError:
                raise TypeError(
                    f"配置项路径无效: {key_path} (检查是否尝试访问非字典项的子项)"
                )

        # 数据库配置
        db_cfg = get_config("database")
        self.database = DatabaseConfig(**db_cfg)

        # route_database loading removed

        # Kafka配置
        kafka_cfg = get_config("kafka")
        if not kafka_cfg.get("bootstrap_servers"):
            raise ValueError(f"在 {config_path} 中必须设置 kafka.bootstrap_servers")
        self.kafka = KafkaConfig(**kafka_cfg)

        # 地图配置 (部分动态)
        map_cfg_static = get_config("map")  # Get static parts like boundary_thickness
        # Use self.location read from top level
        try:
            map_profiles = get_config("map_profiles")
            map_cfg_dynamic = map_profiles[self.location]  # Use self.location here
        except KeyError:
            raise ValueError(
                f"在 config.json 的 'map_profiles' 中未找到地点 '{self.location}' 的配置"  # Updated error message
            )

        # Combine static and dynamic map config
        full_map_cfg = {**map_cfg_static, **map_cfg_dynamic}

        # --- Instantiate nested MapConfig fields ---
        try:
            grid_size_data = full_map_cfg.pop(
                "grid_size"
            )  # Remove before MapConfig init
            min_coords_data = full_map_cfg.pop(
                "min_coords"
            )  # Remove before MapConfig init
            grid_size_obj = GridSizeConfig(**grid_size_data)
            min_coords_obj = MinCoordsConfig(**min_coords_data)
        except KeyError as e:
            raise ValueError(f"地图配置 '{self.location}' 中缺少必需的嵌套字段: {e}")
        except TypeError as e:
            raise ValueError(f"地图配置 '{self.location}' 中嵌套字段格式错误: {e}")

        # Ensure all required fields for MapConfig are present before instantiation
        # Note: We manually handle grid_size and min_coords above
        required_map_fields = [
            f
            for f in MapConfig.__annotations__.keys()
            if f not in ["grid_size", "min_coords"]
        ]
        missing_fields = [
            field for field in required_map_fields if field not in full_map_cfg
        ]
        if missing_fields:
            raise ValueError(
                f"地图配置 '{self.location}' 中缺少必需字段: {', '.join(missing_fields)}"
            )

        # Instantiate MapConfig, then add nested objects
        self.map = MapConfig(
            **full_map_cfg, grid_size=grid_size_obj, min_coords=min_coords_obj
        )
        # --- End MapConfig instantiation ---

        # 路径规划配置
        pp_cfg = get_config("path_planning")
        self.pathplanning = PathPlanningConfig(**pp_cfg)

        # Redis配置
        redis_cfg = get_config("redis")
        self.redis = RedisConfig(**redis_cfg)

        # MQTT配置
        mqtt_cfg = get_config("mqtt")
        self.mqtt = MQTTConfig(**mqtt_cfg)

        # 并行处理配置
        try:
            parallel_cfg = get_config("parallel_processing")
            self.parallel_processing = ParallelProcessingConfig(**parallel_cfg)
        except KeyError:
            # 如果配置文件中没有并行处理配置，使用默认值
            logger.warning("配置文件中未找到并行处理配置，使用默认值")
            self.parallel_processing = ParallelProcessingConfig(
                enabled=False,
                max_workers=4,
                parallel_threshold=5,
                iou_threshold=0.1,
                path_margin=0.01,
                height_diff_threshold=2.0,
            )


# 全局 settings 变量，将在 initialize_settings 中被赋值
settings = None


def initialize_settings(
    config_path: Path = None, location: str = None, server_address: str = None
):
    """
    初始化全局配置实例 settings，确保在日志设置之后调用。

    Args:
        config_path: 可选的配置文件路径。如果不提供，将自动搜索多个位置。
        location: 可选的地点参数，优先级高于配置文件
        server_address: 可选的服务器地址参数，优先级高于配置文件

    Returns:
        Settings: 配置实例
    """
    global settings
    if settings is None:
        settings = Settings(
            config_path=config_path, location=location, server_address=server_address
        )
    return settings


# 不再在此处自动创建实例
# settings = Settings()
