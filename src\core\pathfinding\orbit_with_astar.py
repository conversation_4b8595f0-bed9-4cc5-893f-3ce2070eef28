#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轨道路径规划算法 + A*局部避障
集成垂直避障和A*算法的完整实现
"""

import math
import logging
from typing import List, Optional, Tuple
from heapq import heappush, heappop

from .orbit import OrbitPathFinder
from ..node_3d import GridNode3D

logger = logging.getLogger(__name__)


class OrbitWithAStarPathFinder(OrbitPathFinder):
    """轨道路径规划器 + A*局部避障"""

    def __init__(self, map3d, max_orbit_switches: int = 3):
        super().__init__(map3d)

        # 设置最大轨道切换次数
        self.max_orbit_switches = max_orbit_switches

        # A*搜索参数
        self.astar_search_radius = 4
        self.astar_max_steps = 15
        self.astar_max_candidates = 3

    def _choose_valid_astar_goal(
        self,
        current_pos: Tuple[float, float, float],
        blocked_pos: Tuple[int, int, int],
        current_time: int,
        min_height: int,
        agent_id: str,
        constraints: Optional[List],
        occupancy_map,
    ) -> Optional[Tuple[int, int, int]]:
        """选择A*的目标点并验证可用性"""

        # 候选目标点列表（优先级从高到低）
        candidate_goals = []

        if current_pos[2] <= min_height:
            # 在最小高度，只能向上
            candidate_goals = [
                (blocked_pos[0], blocked_pos[1], blocked_pos[2] + 1),
                (blocked_pos[0], blocked_pos[1], blocked_pos[2] + 2),
                (blocked_pos[0], blocked_pos[1], blocked_pos[2] + 3),
            ]
        else:
            # 优先向上，备选向下
            candidate_goals = [
                (blocked_pos[0], blocked_pos[1], blocked_pos[2] + 1),  # 向上
                (blocked_pos[0], blocked_pos[1], blocked_pos[2] - 1),  # 向下
                (blocked_pos[0], blocked_pos[1], blocked_pos[2] + 2),  # 向上2层
            ]

        # 验证目标点可用性
        estimated_time = current_time + self.astar_max_steps * self.cruise_speed_t

        for goal_pos in candidate_goals[: self.astar_max_candidates]:
            # 检查边界
            if not (
                0 <= goal_pos[0] < self.map.height
                and 0 <= goal_pos[1] < self.map.width
                and min_height <= goal_pos[2] < self.map.depth
            ):
                continue

            # 检查目标点是否可用
            is_valid, _ = self._is_valid_position(
                goal_pos,
                estimated_time,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
            )

            if is_valid:
                logger.debug(f"选择A*目标点: {goal_pos}")
                return goal_pos

        logger.warning("未找到可用的A*目标点")
        return None

    def _simple_heuristic(self, current: GridNode3D, goal: GridNode3D) -> float:
        """简单的曼哈顿距离启发式函数，速度优先"""
        return (
            abs(current.y - goal.y) + abs(current.x - goal.x) + abs(current.z - goal.z)
        )

    def _in_search_bounds(
        self,
        pos: Tuple[float, float, float],
        start_pos: Tuple[float, float, float],
        radius: int,
    ) -> bool:
        """检查位置是否在搜索边界内"""
        return (
            abs(pos[0] - start_pos[0]) <= radius
            and abs(pos[1] - start_pos[1]) <= radius
            and abs(pos[2] - start_pos[2]) <= radius
        )

    def _reconstruct_path(
        self, came_from: dict, current: GridNode3D
    ) -> List[GridNode3D]:
        """重构A*路径"""
        path = [current]
        current_key = (current.y, current.x, current.z)

        while current_key in came_from:
            current = came_from[current_key]
            path.append(current)
            current_key = (current.y, current.x, current.z)

        path.reverse()
        return path

    def _simple_astar(
        self,
        start: GridNode3D,
        goal: GridNode3D,
        occupancy_map,
        radius: int,
        max_steps: int,
        min_height: int,
        agent_id: str,
        constraints: Optional[List],
    ) -> Optional[List[GridNode3D]]:
        """简化的A*实现，专注速度"""

        open_set = []
        heappush(open_set, (0, start))
        came_from = {}
        g_score = {(start.y, start.x, start.z): 0}

        steps = 0
        while open_set and steps < max_steps:
            steps += 1
            current = heappop(open_set)[1]

            # 到达目标附近
            if (
                abs(current.y - goal.y) <= 1
                and abs(current.x - goal.x) <= 1
                and abs(current.z - goal.z) <= 1
            ):
                logger.debug(f"A*搜索成功，步数: {steps}")
                return self._reconstruct_path(came_from, current)

            # 扩展邻居（6个基本方向）
            directions = [
                (0, 1, 0),
                (0, -1, 0),
                (1, 0, 0),
                (-1, 0, 0),
                (0, 0, 1),
                (0, 0, -1),
            ]

            for dy, dx, dz in directions:
                neighbor_pos = (current.y + dy, current.x + dx, current.z + dz)
                neighbor_time = current.t + self.cruise_speed_t

                # 检查搜索边界
                if not self._in_search_bounds(
                    neighbor_pos, (start.y, start.x, start.z), radius
                ):
                    continue

                # 检查位置有效性
                neighbor_pos_int = (
                    int(round(neighbor_pos[0])),
                    int(round(neighbor_pos[1])),
                    int(round(neighbor_pos[2])),
                )

                is_valid, _ = self._is_valid_position(
                    neighbor_pos_int,
                    neighbor_time,
                    min_height,
                    agent_id,
                    constraints,
                    occupancy_map,
                )
                if not is_valid:
                    continue

                neighbor = GridNode3D(
                    neighbor_pos[0], neighbor_pos[1], neighbor_pos[2], neighbor_time
                )
                tentative_g = (
                    g_score.get((current.y, current.x, current.z), float("inf")) + 1
                )

                neighbor_key = (neighbor.y, neighbor.x, neighbor.z)
                if tentative_g < g_score.get(neighbor_key, float("inf")):
                    came_from[neighbor_key] = current
                    g_score[neighbor_key] = tentative_g
                    f_score = tentative_g + self._simple_heuristic(neighbor, goal)
                    heappush(open_set, (f_score, neighbor))

        logger.warning(f"A*搜索失败，已搜索{steps}步")
        return None

    def _local_astar_avoidance(
        self,
        current_pos: Tuple[float, float, float],
        blocked_pos: Tuple[int, int, int],
        current_time: int,
        min_height: int,
        agent_id: str,
        constraints: Optional[List],
        occupancy_map,
    ) -> Optional[List[GridNode3D]]:
        """使用A*进行局部避障，返回完整的避障路径段"""

        # 选择有效的目标点
        astar_goal_pos = self._choose_valid_astar_goal(
            current_pos,
            blocked_pos,
            current_time,
            min_height,
            agent_id,
            constraints,
            occupancy_map,
        )

        if not astar_goal_pos:
            return None

    def _find_cruise_path_with_astar(
        self,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        start_time: int,
        occupancy_map,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """巡航阶段路径规划，集成A*局部避障"""

        path = []
        current_pos = (float(start[0]), float(start[1]), float(start[2]))
        current_time = start_time

        # 轨道相关状态
        current_orbit_zone = None
        current_orbit_idx = -1
        orbit_direction = 1
        orbit_switch_count = 0

        # 目标象限计算
        target_quadrants = self._determine_target_quadrants_by_direction(start, goal)

        max_iterations = 5000
        iteration = 0

        logger.info(f"开始巡航路径规划: {start} -> {goal}")

        while iteration < max_iterations:
            iteration += 1
            print(f"迭代次数: {iteration}")
            if iteration == 10:
                logger.info(f"当前迭代次数: {iteration}")

            # 检查是否到达目标
            distance_to_goal = abs(current_pos[0] - goal[0]) + abs(
                current_pos[1] - goal[1]
            )
            if distance_to_goal < 1.0:
                logger.info(f"成功到达巡航目标，总迭代次数: {iteration}")
                break

            # 创建当前位置节点
            current_node = GridNode3D(
                current_pos[0], current_pos[1], current_pos[2], current_time
            )
            path.append(current_node)

            if current_orbit_zone is None:
                # 当前不在轨道上，尝试直线移动
                next_pos, next_time, obstacle_zone = self._direct_move_towards_goal(
                    current_pos,
                    goal,
                    current_time,
                    min_height,
                    agent_id,
                    constraints,
                    occupancy_map,
                )

                if next_pos is not None:
                    # 直线移动成功
                    current_pos = next_pos
                    current_time = next_time
                else:
                    # 遇到障碍物，需要判断类型
                    if obstacle_zone is None:
                        # 遇到航线占用，使用A*局部避障
                        next_pos_int = (
                            int(round(current_pos[0])),
                            int(round(current_pos[1])),
                            int(round(current_pos[2])),
                        )

                        astar_path_segment = self._local_astar_avoidance(
                            current_pos,
                            next_pos_int,
                            current_time,
                            min_height,
                            agent_id,
                            constraints,
                            occupancy_map,
                        )

                        if astar_path_segment:
                            # 将A*路径段添加到主路径中
                            path.extend(astar_path_segment)

                            # 更新当前状态到A*路径的最后一个节点
                            last_astar_node = astar_path_segment[-1]
                            current_pos = (
                                last_astar_node.y,
                                last_astar_node.x,
                                last_astar_node.z,
                            )
                            current_time = last_astar_node.t

                            logger.info(
                                f"A*避障成功，路径段长度: {len(astar_path_segment)}"
                            )
                            continue
                        else:
                            return None, "A*局部避障失败，无法找到可用目标点或路径"

                    # 遇到固定障碍物，进入轨道系统
                    if obstacle_zone in self.visited_zones:
                        orbit_switch_count += 1
                        if orbit_switch_count > self.max_orbit_switches:
                            return None, f"轨道切换次数过多，可能存在死循环"

                    self.visited_zones.add(obstacle_zone)

                    logger.info(f"遇到禁飞区 {obstacle_zone}，开始搜索轨道点")

                    # 搜索附近的轨道点
                    nearby_point = self._find_nearby_orbit_points(
                        current_pos, obstacle_zone
                    )
                    if not nearby_point:
                        return None, f"无法找到禁飞区 {obstacle_zone} 的轨道点"

                    # 选择最近的轨道点
                    orbit_idx, orbit_point = nearby_point[0]

                    # 进入轨道
                    current_orbit_zone = obstacle_zone
                    current_orbit_idx = orbit_idx
                    current_pos = (orbit_point[0], orbit_point[1], current_pos[2])
                    current_time += self.cruise_speed_t

                    # 选择轨道方向
                    orbit_direction = self._choose_orbit_direction(
                        current_orbit_idx, target_quadrants, obstacle_zone, None
                    )

                    logger.info(f"进入轨道 {obstacle_zone}，索引 {current_orbit_idx}")

            else:
                # 当前在轨道上
                # 检查是否可以退出轨道
                if self._is_in_target_quadrants(
                    current_orbit_idx, target_quadrants, current_orbit_zone
                ) and self._can_exit_orbit_to_goal(
                    current_pos,
                    goal,
                    current_time,
                    min_height,
                    agent_id,
                    constraints,
                    occupancy_map,
                ):

                    logger.info(f"在目标象限中，退出轨道 {current_orbit_zone}")
                    current_orbit_zone = None
                    current_orbit_idx = -1
                    continue

                # 沿轨道移动，检查航线冲突
                orbit_result = self._move_along_orbit_with_collision_check(
                    current_orbit_idx,
                    orbit_direction,
                    current_orbit_zone,
                    current_pos,
                    current_time,
                    min_height,
                    agent_id,
                    constraints,
                    occupancy_map,
                )

                if orbit_result is None:
                    return None, "轨道移动时垂直避障失败"

                current_orbit_idx, current_pos, current_time = orbit_result

        if iteration >= max_iterations:
            return None, f"巡航路径规划超时，迭代次数: {iteration}"

        # 添加最终节点
        if path and (path[-1].y, path[-1].x) != (goal[0], goal[1]):
            final_node = GridNode3D(goal[0], goal[1], goal[2], current_time)
            path.append(final_node)

        return path, None

    def find_path(
        self,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        start_time: Optional[int] = None,
        occupancy_map=None,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """完整的路径规划，集成A*局部避障

        Args:
            start: 起点坐标 (y, x, z)
            goal: 终点坐标 (y, x, z)
            min_height: 最小巡航高度
            agent_id: 智能体ID
            start_time: 起始时间
            occupancy_map: 占用图
            constraints: 约束条件

        Returns:
            (路径节点列表, 错误信息)
        """

        logger.info(f"开始路径规划: {start} -> {goal}, agent: {agent_id}")

        # 重置访问状态
        self.visited_zones.clear()

        try:
            # 1. 垂直起飞阶段
            takeoff_path, error = self._vertical_takeoff(
                start, min_height, agent_id, start_time, occupancy_map, constraints
            )
            if not takeoff_path:
                return None, f"起飞阶段失败: {error}"

            # 2. 巡航阶段（集成A*避障）
            cruise_start = (takeoff_path[-1].y, takeoff_path[-1].x, takeoff_path[-1].z)
            cruise_goal = (goal[0], goal[1], min_height)

            cruise_path, error = self._find_cruise_path_with_astar(
                cruise_start,
                cruise_goal,
                min_height,
                agent_id,
                takeoff_path[-1].t,
                occupancy_map,
                constraints,
            )
            if not cruise_path:
                return None, f"巡航阶段失败: {error}"

            # 3. 垂直降落阶段
            last_cruise_node = cruise_path[-1]
            landing_path, error = self._vertical_landing(
                last_cruise_node,
                goal,
                agent_id,
                occupancy_map,
                constraints,
            )
            if not landing_path:
                return None, f"降落阶段失败: {error}"

            # 4. 合并完整路径
            complete_path = takeoff_path + cruise_path + landing_path

            logger.info(f"路径规划成功，总长度: {len(complete_path)}")
            return complete_path, None

        except Exception as e:
            logger.error(f"路径规划异常: {str(e)}")
            return None, f"路径规划异常: {str(e)}"
