#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试轨道算法 + A*局部避障的集成效果
"""

import sys
import os
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import math

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Microsoft YaHei"]
plt.rcParams["axes.unicode_minus"] = False

from src.core.map.occupancy_map import OccupancyMap
from src.core.pathfinding.orbit_with_astar import OrbitWithAStarPathFinder
from src.core.node_3d import GridNode3D


class MockGridConverter:
    def relative_to_geo(self, y, x, z):
        return {"lat": y * 0.001, "lon": x * 0.001, "alt": z * 10}


class MockObstacleManager:
    def __init__(self):
        # 创建一个圆柱体障碍物
        self.cylinder_center = (300, 400)
        self.cylinder_radius = 60
        self.cylinder_height = 80

        # 生成轨道点
        self._generate_orbit_points()

        # 创建轨道点映射
        self.orbit_points_map = {"cylinder_obstacle": self._create_orbit_points_map()}

    def _generate_orbit_points(self):
        """生成圆柱体周围的轨道点"""
        center_y, center_x = self.cylinder_center
        radius = self.cylinder_radius + 20  # 轨道距离障碍物20个单位

        # 生成32个轨道点
        num_points = 32
        self.orbit_points = []

        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            y = center_y + radius * math.cos(angle)
            x = center_x + radius * math.sin(angle)
            self.orbit_points.append((int(y), int(x)))

    def _create_orbit_points_map(self):
        """创建轨道点的位置映射"""
        orbit_map = {}
        for i, point in enumerate(self.orbit_points):
            orbit_map[point] = i
        return orbit_map

    def get_type_at_position(self, pos):
        """检查位置是否在障碍物内"""
        y, x, z = pos

        # 检查圆柱体障碍物
        if z < self.cylinder_height:
            dy = y - self.cylinder_center[0]
            dx = x - self.cylinder_center[1]
            if dy * dy + dx * dx <= self.cylinder_radius * self.cylinder_radius:
                return ["cylinder_obstacle"]

        return []

    def get_orbit_path(self, zone_name):
        """获取轨道路径"""
        if zone_name == "cylinder_obstacle":
            return self.orbit_points
        return []

    def get_orbit_quadrants(self, zone_name):
        """获取轨道象限"""
        if zone_name == "cylinder_obstacle":
            return {
                "N": list(range(0, 4)),
                "NE": list(range(4, 8)),
                "E": list(range(8, 12)),
                "SE": list(range(12, 16)),
                "S": list(range(16, 20)),
                "SW": list(range(20, 24)),
                "W": list(range(24, 28)),
                "NW": list(range(28, 32)),
            }
        return {}


class MockMap3D:
    def __init__(self):
        self.height = 800
        self.width = 800
        self.depth = 100

        self.obstacle_manager = MockObstacleManager()
        self.grid_converter = MockGridConverter()

    def is_traversable(self, y, x, z):
        """检查位置是否可通行"""
        if not (0 <= y < self.height and 0 <= x < self.width and 0 <= z < self.depth):
            return False

        # 检查是否在障碍物内
        obstacles = self.obstacle_manager.get_type_at_position((y, x, z))
        return len(obstacles) == 0


def test_astar_integration():
    """测试A*集成效果"""
    print("=== 测试轨道算法 + A*局部避障 ===")

    # 创建地图和占用图
    map3d = MockMap3D()
    occupancy_map = OccupancyMap((map3d.height, map3d.width, map3d.depth))

    # 创建路径规划器
    pathfinder = OrbitWithAStarPathFinder(map3d)

    # 添加一条已存在的路径（会与新路径冲突）
    existing_path = []
    for i in range(50):
        # 创建一条对角线路径
        y = 100 + i * 8
        x = 100 + i * 8
        z = 30
        t = i * 10

        node = GridNode3D(y, x, z, t)
        existing_path.append(node)

    # 添加到占用图
    occupancy_map.add_path(existing_path, "existing_agent")

    # 规划一条会冲突的新路径
    start = (50, 50, 10)
    goal = (600, 600, 10)
    min_height = 30

    print(f"起点: {start}")
    print(f"终点: {goal}")
    print(f"最小高度: {min_height}")

    # 执行路径规划
    start_time = time.time()
    path, error = pathfinder.find_path(
        start=start,
        goal=goal,
        min_height=min_height,
        agent_id="test_agent",
        start_time=0,
        occupancy_map=occupancy_map,
    )
    end_time = time.time()

    print(f"规划耗时: {end_time - start_time:.3f}秒")

    if path:
        print(f"路径长度: {len(path)}步")
        print("路径规划成功!")

        # 分析路径特征
        heights = [node.z for node in path]
        min_height_in_path = min(heights)
        max_height_in_path = max(heights)

        print(f"路径高度范围: {min_height_in_path} - {max_height_in_path}")

        # 检查A*避障段
        height_changes = 0
        astar_segments = []

        for i in range(1, len(path)):
            if abs(path[i].z - path[i - 1].z) > 0:
                height_changes += 1
                if height_changes == 1:  # 开始避障
                    segment_start = i - 1
                elif (
                    i == len(path) - 1 or abs(path[i + 1].z - path[i].z) == 0
                ):  # 结束避障
                    astar_segments.append((segment_start, i))

        print(f"高度变化次数: {height_changes}")
        print(f"A*避障段数: {len(astar_segments)}")

        for i, (start_idx, end_idx) in enumerate(astar_segments):
            segment_length = end_idx - start_idx + 1
            segment_height = path[start_idx].z
            print(
                f"  避障段{i+1}: 步骤{start_idx}-{end_idx}, 长度{segment_length}, 高度{segment_height}"
            )

        return map3d, existing_path, path
    else:
        print(f"❌ 路径规划失败: {error}")
        return None


def visualize_astar_result(map3d, existing_path, new_path):
    """可视化A*集成结果"""
    fig = plt.figure(figsize=(15, 10))
    ax = fig.add_subplot(111, projection="3d")

    # 绘制圆柱体障碍物
    cylinder_center = map3d.obstacle_manager.cylinder_center
    cylinder_radius = map3d.obstacle_manager.cylinder_radius
    cylinder_height = map3d.obstacle_manager.cylinder_height

    # 创建圆柱体表面
    theta = np.linspace(0, 2 * np.pi, 50)
    z_cyl = np.linspace(0, cylinder_height, 20)
    theta_mesh, z_mesh = np.meshgrid(theta, z_cyl)

    x_cyl = cylinder_center[1] + cylinder_radius * np.cos(theta_mesh)
    y_cyl = cylinder_center[0] + cylinder_radius * np.sin(theta_mesh)

    ax.plot_surface(x_cyl, y_cyl, z_mesh, alpha=0.3, color="red", label="障碍物")

    # 绘制已存在的路径
    if existing_path:
        existing_x = [node.x for node in existing_path]
        existing_y = [node.y for node in existing_path]
        existing_z = [node.z for node in existing_path]

        ax.plot(
            existing_x,
            existing_y,
            existing_z,
            "b-",
            linewidth=3,
            label="已存在路径",
            alpha=0.7,
        )

    # 绘制新规划的路径，用不同颜色标识A*避障段
    if new_path:
        new_x = [node.x for node in new_path]
        new_y = [node.y for node in new_path]
        new_z = [node.z for node in new_path]

        # 标识不同的路径段
        for i in range(len(new_path) - 1):
            if new_z[i] == 30:  # 正常巡航高度
                color = "green"
                linewidth = 2
            else:  # A*避障段
                color = "orange"
                linewidth = 3

            ax.plot(
                [new_x[i], new_x[i + 1]],
                [new_y[i], new_y[i + 1]],
                [new_z[i], new_z[i + 1]],
                color=color,
                linewidth=linewidth,
            )

        # 标记起点和终点
        ax.scatter(
            new_x[0], new_y[0], new_z[0], c="green", s=150, marker="o", label="起点"
        )
        ax.scatter(
            new_x[-1], new_y[-1], new_z[-1], c="red", s=150, marker="*", label="终点"
        )

    # 设置坐标轴
    ax.set_xlabel("X坐标")
    ax.set_ylabel("Y坐标")
    ax.set_zlabel("高度")
    ax.set_title("轨道算法 + A*局部避障测试")
    ax.legend()

    # 设置视角
    ax.view_init(elev=20, azim=45)

    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    result = test_astar_integration()
    if result:
        map3d, existing_path, new_path = result
        print("\n显示可视化结果...")
        visualize_astar_result(map3d, existing_path, new_path)

    print("\n=== 测试完成 ===")
