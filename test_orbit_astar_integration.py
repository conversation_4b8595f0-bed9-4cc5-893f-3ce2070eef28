#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试轨道算法 + A*局部避障的集成效果
"""

import sys
import os
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import math

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Microsoft YaHei"]
plt.rcParams["axes.unicode_minus"] = False

from src.core.map.occupancy_map import OccupancyMap
from src.core.pathfinding.orbit_with_astar import OrbitWithAStarPathFinder
from src.core.node_3d import GridNode3D


class MockGridConverter:
    def relative_to_geo(self, y, x, z):
        return {"lat": y * 0.001, "lon": x * 0.001, "alt": z * 10}


class MockObstacleManager:
    def __init__(self):
        # 创建一个小圆柱体障碍物（适合20x20x20地图）
        self.cylinder_center = (10, 10)
        self.cylinder_radius = 3
        self.cylinder_height = 15

        # 生成轨道点
        self._generate_orbit_points()

        # 创建轨道点映射
        self.orbit_points_map = {"cylinder_obstacle": self._create_orbit_points_map()}

    def _generate_orbit_points(self):
        """生成圆柱体周围的轨道点"""
        center_y, center_x = self.cylinder_center
        radius = self.cylinder_radius + 2  # 轨道距离障碍物2个单位

        # 生成16个轨道点（小地图用较少轨道点）
        num_points = 16
        self.orbit_points = []

        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            y = center_y + radius * math.cos(angle)
            x = center_x + radius * math.sin(angle)
            self.orbit_points.append((int(y), int(x)))

    def _create_orbit_points_map(self):
        """创建轨道点的位置映射"""
        orbit_map = {}
        for i, point in enumerate(self.orbit_points):
            orbit_map[point] = i
        return orbit_map

    def get_type_at_position(self, pos):
        """检查位置是否在障碍物内"""
        y, x, z = pos

        # 检查圆柱体障碍物
        if z < self.cylinder_height:
            dy = y - self.cylinder_center[0]
            dx = x - self.cylinder_center[1]
            if dy * dy + dx * dx <= self.cylinder_radius * self.cylinder_radius:
                return ["cylinder_obstacle"]

        return []

    def get_orbit_path(self, zone_name):
        """获取轨道路径"""
        if zone_name == "cylinder_obstacle":
            return self.orbit_points
        return []

    def get_orbit_quadrants(self, zone_name):
        """获取轨道象限"""
        if zone_name == "cylinder_obstacle":
            return {
                "N": list(range(0, 2)),
                "NE": list(range(2, 4)),
                "E": list(range(4, 6)),
                "SE": list(range(6, 8)),
                "S": list(range(8, 10)),
                "SW": list(range(10, 12)),
                "W": list(range(12, 14)),
                "NW": list(range(14, 16)),
            }
        return {}


class MockMap3D:
    def __init__(self):
        self.height = 20
        self.width = 20
        self.depth = 20

        self.obstacle_manager = MockObstacleManager()
        self.grid_converter = MockGridConverter()

    def is_traversable(self, y, x, z):
        """检查位置是否可通行"""
        if not (0 <= y < self.height and 0 <= x < self.width and 0 <= z < self.depth):
            return False

        # 检查是否在障碍物内
        obstacles = self.obstacle_manager.get_type_at_position((y, x, z))
        return len(obstacles) == 0


def test_direct_line_collision():
    """测试场景1：直线前进时遇到航线占用"""
    print("=== 测试场景1：直线前进时遇到航线占用 ===")

    # 创建地图和占用图
    map3d = MockMap3D()
    occupancy_map = OccupancyMap((map3d.height, map3d.width, map3d.depth), 5)

    # 创建路径规划器
    pathfinder = OrbitWithAStarPathFinder(map3d)

    # 添加一条已存在的对角线路径（会与新路径冲突）
    existing_path = []
    for i in range(8):
        # 创建一条从(2,2)到(9,9)的对角线路径
        y = 6 - i
        x = i
        z = 10  # 在巡航高度
        t = i

        node = GridNode3D(y, x, z, t)
        existing_path.append(node)

    # 添加到占用图
    occupancy_map.add_path(existing_path, "existing_agent")

    # 规划一条会冲突的新路径（也是对角线，会在中间相交）
    start = (1, 1, 5)
    goal = (18, 18, 5)
    min_height = 10

    print(f"起点: {start}")
    print(f"终点: {goal}")
    print(f"最小高度: {min_height}")
    print(f"已存在路径: (2,2) -> (9,9) 在高度{existing_path[0].z}")

    # 执行路径规划
    start_time = time.time()
    path, error = pathfinder.find_path(
        start=start,
        goal=goal,
        min_height=min_height,
        agent_id="test_agent_1",
        start_time=0,
        occupancy_map=occupancy_map,
    )
    end_time = time.time()

    print(f"规划耗时: {end_time - start_time:.3f}秒")

    if path:
        print(f"路径长度: {len(path)}步")
        print("✅ 路径规划成功!")

        # 分析避障效果
        analyze_avoidance_effect(path, "直线避障")

        return map3d, existing_path, path, "direct_collision"
    else:
        print(f"❌ 路径规划失败: {error}")
        return None


def test_orbit_collision():
    """测试场景2：轨道上遇到航线占用"""
    print("\n=== 测试场景2：轨道上遇到航线占用 ===")

    # 创建地图和占用图
    map3d = MockMap3D()
    occupancy_map = OccupancyMap((map3d.height, map3d.width, map3d.depth), 5)

    # 创建路径规划器
    pathfinder = OrbitWithAStarPathFinder(map3d)

    # 在轨道上添加一条占用路径
    orbit_points = map3d.obstacle_manager.orbit_points
    existing_path = []

    # 占用轨道点2-6（轨道的一段）
    for i in range(2, 7):
        point = orbit_points[i]
        node = GridNode3D(point[0], point[1], 10, i)  # 在巡航高度
        existing_path.append(node)

    # 添加到占用图
    occupancy_map.add_path(existing_path, "orbit_agent")

    # 规划一条需要经过障碍物的路径（会进入轨道并遇到占用）
    start = (5, 5, 5)
    goal = (15, 15, 5)  # 这条路径需要绕过圆柱体障碍物
    min_height = 10

    print(f"起点: {start}")
    print(f"终点: {goal}")
    print(f"最小高度: {min_height}")
    print(f"障碍物中心: {map3d.obstacle_manager.cylinder_center}")
    print(f"轨道占用段: 轨道点{2}-{6} 在高度{existing_path[0].z}")

    # 执行路径规划
    start_time = time.time()
    path, error = pathfinder.find_path(
        start=start,
        goal=goal,
        min_height=min_height,
        agent_id="test_agent_2",
        start_time=100,  # 延迟起飞避免时间冲突
        occupancy_map=occupancy_map,
    )
    end_time = time.time()

    print(f"规划耗时: {end_time - start_time:.3f}秒")

    if path:
        print(f"路径长度: {len(path)}步")
        print("✅ 路径规划成功!")

        # 分析避障效果
        analyze_avoidance_effect(path, "轨道避障")

        return map3d, existing_path, path, "orbit_collision"
    else:
        print(f"❌ 路径规划失败: {error}")
        return None


def analyze_avoidance_effect(path, test_type):
    """分析避障效果"""
    print(f"\n--- {test_type} 分析 ---")

    heights = [node.z for node in path]
    min_height = min(heights)
    max_height = max(heights)

    print(f"路径高度范围: {min_height} - {max_height}")

    # 检查高度变化
    height_changes = 0
    avoidance_segments = []

    for i in range(1, len(path)):
        if abs(path[i].z - path[i - 1].z) > 0.1:
            height_changes += 1

    print(f"高度变化次数: {height_changes}")

    if max_height > min_height:
        print(f"✅ 检测到垂直避障，最大避障高度: {max_height}")

        # 找出避障段
        in_avoidance = False
        segment_start = 0

        for i, node in enumerate(path):
            if node.z > min_height and not in_avoidance:
                # 开始避障
                in_avoidance = True
                segment_start = i
            elif node.z <= min_height and in_avoidance:
                # 结束避障
                in_avoidance = False
                avoidance_segments.append((segment_start, i - 1))

        # 处理最后一段
        if in_avoidance:
            avoidance_segments.append((segment_start, len(path) - 1))

        print(f"避障段数: {len(avoidance_segments)}")
        for i, (start_idx, end_idx) in enumerate(avoidance_segments):
            segment_length = end_idx - start_idx + 1
            segment_height = path[start_idx].z
            print(
                f"  避障段{i+1}: 步骤{start_idx}-{end_idx}, 长度{segment_length}, 高度{segment_height}"
            )
    else:
        print("ℹ️  未检测到垂直避障")


def visualize_astar_result(map3d, existing_path, new_path):
    """可视化A*集成结果"""
    fig = plt.figure(figsize=(15, 10))
    ax = fig.add_subplot(111, projection="3d")

    # 绘制圆柱体障碍物
    cylinder_center = map3d.obstacle_manager.cylinder_center
    cylinder_radius = map3d.obstacle_manager.cylinder_radius
    cylinder_height = map3d.obstacle_manager.cylinder_height

    # 创建圆柱体表面
    theta = np.linspace(0, 2 * np.pi, 50)
    z_cyl = np.linspace(0, cylinder_height, 20)
    theta_mesh, z_mesh = np.meshgrid(theta, z_cyl)

    x_cyl = cylinder_center[1] + cylinder_radius * np.cos(theta_mesh)
    y_cyl = cylinder_center[0] + cylinder_radius * np.sin(theta_mesh)

    ax.plot_surface(x_cyl, y_cyl, z_mesh, alpha=0.3, color="red", label="障碍物")

    # 绘制已存在的路径
    if existing_path:
        existing_x = [node.x for node in existing_path]
        existing_y = [node.y for node in existing_path]
        existing_z = [node.z for node in existing_path]

        ax.plot(
            existing_x,
            existing_y,
            existing_z,
            "b-",
            linewidth=3,
            label="已存在路径",
            alpha=0.7,
        )

    # 绘制新规划的路径，用不同颜色标识A*避障段
    if new_path:
        new_x = [node.x for node in new_path]
        new_y = [node.y for node in new_path]
        new_z = [node.z for node in new_path]

        # 标识不同的路径段
        for i in range(len(new_path) - 1):
            if new_z[i] == 30:  # 正常巡航高度
                color = "green"
                linewidth = 2
            else:  # A*避障段
                color = "orange"
                linewidth = 3

            ax.plot(
                [new_x[i], new_x[i + 1]],
                [new_y[i], new_y[i + 1]],
                [new_z[i], new_z[i + 1]],
                color=color,
                linewidth=linewidth,
            )

        # 标记起点和终点
        ax.scatter(
            new_x[0], new_y[0], new_z[0], c="green", s=150, marker="o", label="起点"
        )
        ax.scatter(
            new_x[-1], new_y[-1], new_z[-1], c="red", s=150, marker="*", label="终点"
        )

    # 设置坐标轴范围（适应小地图）
    ax.set_xlim(0, 20)
    ax.set_ylim(0, 20)
    ax.set_zlim(0, 20)

    ax.set_xlabel("X坐标")
    ax.set_ylabel("Y坐标")
    ax.set_zlabel("高度")
    ax.set_title("轨道算法 + A*局部避障测试 (20x20x20)")
    ax.legend()

    # 设置视角
    ax.view_init(elev=20, azim=45)

    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    print("=== 轨道算法 + A*局部避障 综合测试 ===\n")

    # 测试场景1：直线避障
    # result1 = test_direct_line_collision()
    # if result1:
    #     map3d, existing_path, new_path, test_type = result1
    #     print("\n显示直线避障可视化结果...")
    #     visualize_astar_result(map3d, existing_path, new_path)

    # 测试场景2：轨道避障
    result2 = test_orbit_collision()
    if result2:
        map3d, existing_path, new_path, test_type = result2
        print("\n显示轨道避障可视化结果...")
        visualize_astar_result(map3d, existing_path, new_path)

    print("\n=== 所有测试完成 ===")
