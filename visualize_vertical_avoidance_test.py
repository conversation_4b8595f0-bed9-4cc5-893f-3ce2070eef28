#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
垂直避障功能三维可视化测试
测试两种情况：
1. 直行时遇到航线占用
2. 轨道上遇到航线占用
"""

import sys
import os
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
from matplotlib.animation import FuncAnimation
import math

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Microsoft YaHei"]
plt.rcParams["axes.unicode_minus"] = False

# from src.core.map.map3d import Map3D
from src.core.map.occupancy_map import OccupancyMap
from src.core.pathfinding.orbit import OrbitPathFinder
from src.core.node_3d import GridNode3D


class MockGridConverter:
    def relative_to_geo(self, y, x, z):
        return {"lat": y * 0.001, "lon": x * 0.001, "alt": z * 10}


class MockObstacleManager:
    def __init__(self):
        # 创建一个圆柱体障碍物
        self.cylinder_center = (300, 400)
        self.cylinder_radius = 60
        self.cylinder_height = 80

        # 生成轨道点
        self._generate_orbit_points()

    def _generate_orbit_points(self):
        """生成圆柱体周围的轨道点"""
        center_y, center_x = self.cylinder_center
        radius = self.cylinder_radius + 20  # 轨道距离障碍物20个单位

        # 生成32个轨道点
        num_points = 32
        self.orbit_points = []

        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            y = center_y + radius * math.cos(angle)
            x = center_x + radius * math.sin(angle)
            self.orbit_points.append((int(y), int(x)))

    def get_type_at_position(self, pos):
        """检查位置是否在障碍物内"""
        y, x, z = pos

        # 检查圆柱体障碍物
        if z < self.cylinder_height:
            dy = y - self.cylinder_center[0]
            dx = x - self.cylinder_center[1]
            if dy * dy + dx * dx <= self.cylinder_radius * self.cylinder_radius:
                return ["cylinder_obstacle"]

        return []

    def get_orbit_path(self, zone_name):
        """获取轨道路径"""
        if zone_name == "cylinder_obstacle":
            return self.orbit_points
        return []

    def get_orbit_quadrants(self, zone_name):
        """获取轨道象限"""
        if zone_name == "cylinder_obstacle":
            # 简化的象限定义
            return {
                "N": list(range(0, 4)),
                "NE": list(range(4, 8)),
                "E": list(range(8, 12)),
                "SE": list(range(12, 16)),
                "S": list(range(16, 20)),
                "SW": list(range(20, 24)),
                "W": list(range(24, 28)),
                "NW": list(range(28, 32)),
            }
        return {}


class MockMap3D:
    def __init__(self):
        self.height = 800
        self.width = 800
        self.depth = 100

        self.obstacle_manager = MockObstacleManager()
        self.grid_converter = MockGridConverter()

    def is_traversable(self, y, x, z):
        """检查位置是否可通行"""
        if not (0 <= y < self.height and 0 <= x < self.width and 0 <= z < self.depth):
            return False

        # 检查是否在障碍物内
        obstacles = self.obstacle_manager.get_type_at_position((y, x, z))
        return len(obstacles) == 0


def create_test_scenario():
    """创建测试场景"""
    # 创建地图
    map3d = MockMap3D()

    # 创建占用图
    occupancy_map = OccupancyMap((map3d.height, map3d.width, map3d.depth))

    # 创建路径规划器
    pathfinder = OrbitPathFinder(map3d)

    return map3d, occupancy_map, pathfinder


def add_existing_path_to_occupancy(occupancy_map):
    """向占用图添加一条已存在的路径"""
    # 创建一条从(100, 100, 30)到(700, 700, 30)的直线路径
    existing_path = []
    start_pos = (100, 100, 30)
    end_pos = (700, 700, 30)

    # 生成直线路径
    steps = 100
    for i in range(steps + 1):
        t = i / steps
        y = start_pos[0] + t * (end_pos[0] - start_pos[0])
        x = start_pos[1] + t * (end_pos[1] - start_pos[1])
        z = start_pos[2]

        node = GridNode3D(y, x, z, i * 10)  # 每10个时间单位一步
        existing_path.append(node)

    # 添加到占用图
    for node in existing_path:
        pos = (round(node.y), round(node.x), round(node.z))
        occupancy_map.add_position("existing_agent", pos, node.t)

    return existing_path


def test_direct_collision_avoidance():
    """测试直行时遇到航线占用的避障"""
    print("=== 测试1: 直行时遇到航线占用 ===")

    map3d, occupancy_map, pathfinder = create_test_scenario()

    # 添加已存在的路径
    existing_path = add_existing_path_to_occupancy(occupancy_map)

    # 规划一条会与已存在路径冲突的新路径
    start = (50, 50, 30)
    goal = (750, 750, 30)
    min_height = 30

    print(f"起点: {start}")
    print(f"终点: {goal}")
    print(f"最小高度: {min_height}")

    # 执行路径规划
    start_time = time.time()
    path, error = pathfinder.find_path(
        start=start,
        goal=goal,
        min_height=min_height,
        agent_id="test_agent",
        start_time=0,
        occupancy_map=occupancy_map,
    )
    end_time = time.time()

    print(f"规划耗时: {end_time - start_time:.3f}秒")

    if path:
        print(f"路径长度: {len(path)}步")
        print("路径规划成功!")

        # 检查是否有垂直避障
        heights = [node.z for node in path]
        max_height = max(heights)
        min_height_in_path = min(heights)

        print(f"路径高度范围: {min_height_in_path} - {max_height}")
        if max_height > 30:
            print(f"✅ 检测到垂直避障，最大高度: {max_height}")

        return map3d, existing_path, path, "direct_collision"
    else:
        print(f"❌ 路径规划失败: {error}")
        return None


def test_orbit_collision_avoidance():
    """测试轨道上遇到航线占用的避障"""
    print("\n=== 测试2: 轨道上遇到航线占用 ===")

    map3d, occupancy_map, pathfinder = create_test_scenario()

    # 在轨道上添加一条占用路径
    orbit_points = map3d.obstacle_manager.orbit_points
    orbit_path = []

    # 选择轨道上的一段作为已占用路径
    for i in range(10, 20):  # 占用轨道点10-20
        point = orbit_points[i % len(orbit_points)]
        node = GridNode3D(point[0], point[1], 30, i * 10)
        orbit_path.append(node)

        pos = (round(node.y), round(node.x), round(node.z))
        occupancy_map.add_position("orbit_agent", pos, node.t)

    # 规划一条需要经过障碍物的路径
    start = (200, 200, 30)
    goal = (500, 600, 30)  # 这条路径需要绕过圆柱体障碍物
    min_height = 30

    print(f"起点: {start}")
    print(f"终点: {goal}")
    print(f"最小高度: {min_height}")

    # 执行路径规划
    start_time = time.time()
    path, error = pathfinder.find_path(
        start=start,
        goal=goal,
        min_height=min_height,
        agent_id="test_agent2",
        start_time=100,  # 延迟起飞避免时间冲突
        occupancy_map=occupancy_map,
    )
    end_time = time.time()

    print(f"规划耗时: {end_time - start_time:.3f}秒")

    if path:
        print(f"路径长度: {len(path)}步")
        print("路径规划成功!")

        # 检查是否有垂直避障
        heights = [node.z for node in path]
        max_height = max(heights)
        min_height_in_path = min(heights)

        print(f"路径高度范围: {min_height_in_path} - {max_height}")
        if max_height > 30:
            print(f"✅ 检测到垂直避障，最大高度: {max_height}")

        return map3d, orbit_path, path, "orbit_collision"
    else:
        print(f"❌ 路径规划失败: {error}")
        return None


def visualize_3d_paths(map3d, existing_path, new_path, test_type):
    """三维可视化路径"""
    fig = plt.figure(figsize=(15, 10))
    ax = fig.add_subplot(111, projection="3d")

    # 绘制圆柱体障碍物
    cylinder_center = map3d.obstacle_manager.cylinder_center
    cylinder_radius = map3d.obstacle_manager.cylinder_radius
    cylinder_height = map3d.obstacle_manager.cylinder_height

    # 创建圆柱体表面
    theta = np.linspace(0, 2 * np.pi, 50)
    z_cyl = np.linspace(0, cylinder_height, 20)
    theta_mesh, z_mesh = np.meshgrid(theta, z_cyl)

    x_cyl = cylinder_center[1] + cylinder_radius * np.cos(theta_mesh)
    y_cyl = cylinder_center[0] + cylinder_radius * np.sin(theta_mesh)

    ax.plot_surface(x_cyl, y_cyl, z_mesh, alpha=0.3, color="red", label="障碍物")

    # 绘制轨道点
    orbit_points = map3d.obstacle_manager.orbit_points
    orbit_x = [p[1] for p in orbit_points]
    orbit_y = [p[0] for p in orbit_points]
    orbit_z = [30] * len(orbit_points)  # 轨道在高度30

    ax.scatter(orbit_x, orbit_y, orbit_z, c="orange", s=20, alpha=0.6, label="轨道点")

    # 绘制已存在的路径
    if existing_path:
        existing_x = [node.x for node in existing_path]
        existing_y = [node.y for node in existing_path]
        existing_z = [node.z for node in existing_path]

        ax.plot(
            existing_x,
            existing_y,
            existing_z,
            "b-",
            linewidth=3,
            label="已存在路径",
            alpha=0.7,
        )
        ax.scatter(
            existing_x[0],
            existing_y[0],
            existing_z[0],
            c="blue",
            s=100,
            marker="s",
            label="已存在路径起点",
        )
        ax.scatter(
            existing_x[-1],
            existing_y[-1],
            existing_z[-1],
            c="blue",
            s=100,
            marker="^",
            label="已存在路径终点",
        )

    # 绘制新规划的路径
    if new_path:
        new_x = [node.x for node in new_path]
        new_y = [node.y for node in new_path]
        new_z = [node.z for node in new_path]

        # 用不同颜色表示不同高度
        colors = plt.cm.viridis(np.array(new_z) / max(new_z))

        for i in range(len(new_path) - 1):
            ax.plot(
                [new_x[i], new_x[i + 1]],
                [new_y[i], new_y[i + 1]],
                [new_z[i], new_z[i + 1]],
                color=colors[i],
                linewidth=2,
            )

        ax.scatter(
            new_x[0],
            new_y[0],
            new_z[0],
            c="green",
            s=150,
            marker="o",
            label="新路径起点",
        )
        ax.scatter(
            new_x[-1],
            new_y[-1],
            new_z[-1],
            c="red",
            s=150,
            marker="*",
            label="新路径终点",
        )

        # 标注高度变化点
        heights = [node.z for node in new_path]
        for i in range(1, len(heights)):
            if heights[i] != heights[i - 1]:
                ax.text(new_x[i], new_y[i], new_z[i], f"H:{heights[i]}", fontsize=8)

    # 设置坐标轴
    ax.set_xlabel("X坐标")
    ax.set_ylabel("Y坐标")
    ax.set_zlabel("高度")
    ax.set_title(f"垂直避障测试 - {test_type}")
    ax.legend()

    # 设置视角
    ax.view_init(elev=20, azim=45)

    plt.tight_layout()
    plt.show()


def analyze_path_avoidance(new_path, existing_path, test_type):
    """分析路径避障效果"""
    print(f"\n--- {test_type} 避障分析 ---")

    if not new_path:
        print("❌ 无新路径可分析")
        return

    # 分析高度变化
    heights = [node.z for node in new_path]
    min_height = min(heights)
    max_height = max(heights)
    height_changes = 0

    for i in range(1, len(heights)):
        if heights[i] != heights[i - 1]:
            height_changes += 1

    print(f"📊 路径统计:")
    print(f"  - 总步数: {len(new_path)}")
    print(f"  - 高度范围: {min_height} - {max_height}")
    print(f"  - 高度变化次数: {height_changes}")

    # 检查是否有避障行为
    if max_height > min_height:
        print(f"✅ 检测到垂直避障，最大避障高度: {max_height}")

        # 找出避障段
        avoidance_segments = []
        current_segment = []

        for i, node in enumerate(new_path):
            if node.z > min_height:
                current_segment.append(i)
            else:
                if current_segment:
                    avoidance_segments.append(current_segment)
                    current_segment = []

        if current_segment:
            avoidance_segments.append(current_segment)

        print(f"  - 避障段数: {len(avoidance_segments)}")
        for i, segment in enumerate(avoidance_segments):
            start_idx, end_idx = segment[0], segment[-1]
            segment_height = new_path[start_idx].z
            print(f"    段{i+1}: 步骤{start_idx}-{end_idx}, 高度{segment_height}")
    else:
        print("ℹ️  未检测到垂直避障")

    # 检查与已存在路径的冲突
    if existing_path:
        conflicts = check_path_conflicts(new_path, existing_path)
        if conflicts:
            print(f"⚠️  检测到 {len(conflicts)} 个潜在冲突点")
        else:
            print("✅ 无路径冲突")


def check_path_conflicts(path1, path2):
    """检查两条路径是否有冲突"""
    conflicts = []

    # 创建路径1的时空占用字典
    path1_occupancy = {}
    for node in path1:
        pos = (round(node.y), round(node.x), round(node.z))
        if pos not in path1_occupancy:
            path1_occupancy[pos] = []
        path1_occupancy[pos].append(node.t)

    # 检查路径2是否与路径1冲突
    for node in path2:
        pos = (round(node.y), round(node.x), round(node.z))
        if pos in path1_occupancy:
            for t1 in path1_occupancy[pos]:
                if abs(node.t - t1) <= 5:  # 时间容差
                    conflicts.append((pos, node.t, t1))

    return conflicts


def run_comprehensive_test():
    """运行完整测试并可视化"""
    print("=== 垂直避障功能综合测试 ===\n")

    # 测试1: 直行避障
    result1 = test_direct_collision_avoidance()
    if result1:
        map3d, existing_path, new_path, test_type = result1
        analyze_path_avoidance(new_path, existing_path, "直行避障")
        print("\n显示直行避障可视化...")
        visualize_3d_paths(map3d, existing_path, new_path, "直行时遇到航线占用")

    # 测试2: 轨道避障
    result2 = test_orbit_collision_avoidance()
    if result2:
        map3d, existing_path, new_path, test_type = result2
        analyze_path_avoidance(new_path, existing_path, "轨道避障")
        print("\n显示轨道避障可视化...")
        visualize_3d_paths(map3d, existing_path, new_path, "轨道上遇到航线占用")

    print("\n=== 所有测试完成 ===")


if __name__ == "__main__":
    run_comprehensive_test()
