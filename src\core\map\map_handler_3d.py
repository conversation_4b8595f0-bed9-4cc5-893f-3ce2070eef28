import numpy as np

# import mysql.connector # Removed unused import
import os
import sys  # Add sys import for cache path logic
import math

# import time  # 不再需要
from typing import List, Set, Tuple, Dict, Optional
from ...config import settings  # Corrected import
from .obstacle_manager import ObstacleTypeManager

# Import GridConverter only, get_resource_path is removed from grid_converter and likely not needed here
from .grid_converter import GridConverter

from .optimized_polygon_check_v2 import (
    PolygonIndex,
    points_in_polygon_vectorized,
    is_point_in_polygon_precise,
)

# 移除不存在的模块引用
from .boundary_extractor import (
    generate_polygon_boundary_points_3d,
    generate_cylinder_boundary_points_3d,
)
from ...utils.logging import get_logger

logger = get_logger(__name__)

# Removed module-level settings access
# DEFAULT_BOUNDARY_THICKNESS = settings.map.boundary_thickness
# DEFAULT_BUFFER_DISTANCE_METERS = settings.map.buffer_distance_meters


class Map3D:
    def __init__(
        self,
        height: int = None,
        width: int = None,
        depth: int = None,
        fixed_obstacles=True,
        force_reload=False,
    ):
        """
        初始化3D地图

        Args:
            height: 地图高度，默认从配置获取
            width: 地图宽度，默认从配置获取
            depth: 地图深度，默认从配置获取
            fixed_obstacles: 是否加载固定障碍物
            force_reload: 是否强制重新加载障碍物缓存
        """
        # 使用传入值或配置值
        self.height = height or settings.settings.map.height  # Corrected access
        self.width = width or settings.settings.map.width  # Corrected access
        self.depth = depth or settings.settings.map.depth  # Corrected access
        # Removed database related attributes
        # self.database = database or settings.settings.database.database # Corrected access
        # self.user = user or settings.database.user
        # self.password = password or settings.database.password
        # self.host = host or settings.database.host
        # self.port = int(port) if port else settings.settings.database.port # Corrected access

        # 缓存文件路径 based on settings.location
        cache_dir_name = (
            settings.settings.location
        )  # Use location name for cache directory # Corrected access
        if getattr(sys, "frozen", False):
            # 打包环境
            cache_base_dir = os.path.join(sys._MEIPASS, "cache", cache_dir_name)
        else:
            # 开发环境
            cache_base_dir = os.path.join(
                os.path.dirname(__file__), "cache", cache_dir_name
            )
        # Ensure cache directory exists
        os.makedirs(cache_base_dir, exist_ok=True)
        self.cache_file = os.path.join(cache_base_dir, "fixed_obstacles_cache.npz")

        # Removed self.dbconfig as it's no longer needed by GridConverter or obstacle loading
        # self.dbconfig = { ... }

        # 初始化障碍物管理器
        self.obstacle_manager = ObstacleTypeManager(self.height, self.width, self.depth)

        # 用于缓存固定障碍物的numpy数组（仅用于加载和缓存）
        # self.fixed_obstacles = np.zeros(
        #     (self.height, self.width, self.depth), dtype=int
        # )

        # 初始化坐标转换器, passing the map config from settings
        self.grid_converter = GridConverter(settings.settings.map)  # Corrected access

        # 初始化可通行点集合
        # 所有点都是可通行的，直到被标记为障碍物
        # self.traversable_positions = set(
        #     (y, x, z)
        #     for y in range(self.height)
        #     for x in range(self.width)
        #     for z in range(self.depth)
        # )

        # 初始化不可通行点集合
        # 默认所有点都是可通行的，只存储不可通行的点
        self.non_traversable: Set[Tuple[int, int, int]] = set()
        # 存储禁飞区几何定义: zone_name -> {'type': 'cylinder'|'polygon', ...}
        self.nfz_definitions: Dict[str, Dict] = {}

        # 初始化多边形空间索引，用于加速点在多边形内的检测
        self.polygon_index = PolygonIndex()

        # 初始化地图并加载障碍物
        if fixed_obstacles:
            self.obstacle_manager.register_type("fixed", "固定障碍物")
            self._load_obstacles(force_reload)

    def _load_obstacles_from_cache(self):
        """从本地文件加载障碍物数据"""
        if os.path.exists(self.cache_file):
            try:
                data = np.load(self.cache_file)

                # 检查文件格式
                if "positions" in data:
                    # 新格式：直接加载位置坐标
                    positions_array = data["positions"]
                    obstacle_points = set(map(tuple, positions_array))
                elif "obstacles" in data:
                    # 旧格式：从布尔数组中提取位置
                    obstacle_points = set(
                        map(tuple, np.argwhere(data["obstacles"] == 1))
                    )
                else:
                    logger.error("缓存文件格式无效")
                    return False

                # 批量更新障碍物管理器
                self.obstacle_manager.add_positions_batch("fixed", obstacle_points)

                # 更新不可通行点集合
                self.non_traversable.update(obstacle_points)

                logger.info(
                    f"从fixed_obstacles_cache.npz缓存加载了 {len(obstacle_points)} 个固定障碍物"
                )
                return True
            except Exception as e:
                logger.info(f"读取缓存文件失败: {e}")
        return False

    def _save_obstacles_to_cache(self):
        """将障碍物数据保存到本地文件"""
        try:
            # 获取固定障碍物的位置集合
            obstacle_positions = self.obstacle_manager.get_positions("fixed")

            # 直接保存位置坐标列表
            if obstacle_positions:
                # 将位置集合转换为数组
                positions_array = np.array(list(obstacle_positions))
                # 保存到文件，使用稀疏格式
                np.savez(self.cache_file, positions=positions_array)
            else:
                # 如果没有障碍物，保存一个空数组
                np.savez(self.cache_file, positions=np.array([]))

            logger.info(f"已将{len(obstacle_positions)}个障碍物数据保存到缓存文件")
        except Exception as e:
            logger.error(f"保存缓存文件失败: {e}")

    def _load_obstacles(self, force_reload=False):
        """加载固定障碍物并添加26个邻域点作为安全缓冲区"""
        # 如果不是强制重新加载且成功从缓存加载
        if not force_reload and self._load_obstacles_from_cache():
            return

        logger.info(
            f"无法从fixed_obstacles_cache.npz缓存加载了固定障碍物，请检查缓存文件是否存在"
        )

        self.obstacle_manager.unregister_type("fixed")

    def in_bounds(self, y, x, z):
        """检查给定坐标是否在地图范围内"""
        return (
            (0 <= x < self.width) and (0 <= y < self.height) and (0 <= z < self.depth)
        )

    def traversable(self, y, x, z):
        """检查单元格是否可通过（非障碍物）"""
        return (y, x, z) not in self.non_traversable

    def get_neighbors(self, y, x, z):
        """
        获取给定位置的所有有效相邻位置
        返回: 坐标元组列表 (y, x, z)
        """
        neighbors = []
        # 定义3D空间中的可能移动（6-连通性）
        deltas = [
            (0, 0, 1),  # 前
            (0, 0, -1),  # 后
            (0, 1, 0),  # 右
            (0, -1, 0),  # 左
            (1, 0, 0),  # 上
            (-1, 0, 0),  # 下
        ]

        for dy, dx, dz in deltas:
            new_y, new_x, new_z = y + dy, x + dx, z + dz
            if self.in_bounds(new_y, new_x, new_z) and self.traversable(
                new_y, new_x, new_z
            ):
                neighbors.append((new_y, new_x, new_z))

        return neighbors

    def _check_no_fly_zone_conflicts(
        self, obstacle_points: set, planned_paths: dict = None
    ):
        """
        检测禁飞区与现有航路的冲突

        Args:
            obstacle_points: 禁飞区的点集合，每个元素是(y, x, z)元组
            planned_paths: 路径信息字典 {flight_id: flight_info}

        Returns:
            dict: 航路冲突信息，格式为:
            {
                'agent_1': {
                    'segments': [
                        {
                            'start': point_info,      # 点的完整信息（包含地理和网格坐标）
                            'end': point_info,        # 点的完整信息
                            'start_idx': idx1,        # 起点在原路径中的索引
                            'end_idx': idx2,          # 终点在原路径中的索引
                            'points': 5               # 该段包含的点数
                        },
                        ...
                    ],
                    'total_points': 8  # 总冲突点数
                }
            }
        """
        conflicts = {}
        if not planned_paths:
            return conflicts

        for agent_id, flight_info in planned_paths.items():
            path = flight_info.get("path", [])
            if not path:
                continue

            # 获取所有点的网格坐标
            positions = []
            for point in path:
                grid = point.get("grid", {})
                positions.append((grid["y"], grid["x"], grid["z"]))

            # 使用集合操作更高效地检测冲突
            positions_set = set(positions)
            conflict_positions = positions_set.intersection(obstacle_points)

            if conflict_positions:
                # 创建冲突掩码
                conflict_mask = [pos in conflict_positions for pos in positions]
                conflict_mask_int = [1 if c else 0 for c in conflict_mask]
                transitions = [
                    conflict_mask_int[i + 1] - conflict_mask_int[i]
                    for i in range(len(conflict_mask_int) - 1)
                ]

                enter_indices = [i for i, t in enumerate(transitions) if t == 1]
                exit_indices = [i for i, t in enumerate(transitions) if t == -1]

                # 处理开始和结束的边界情况
                if conflict_mask[0]:
                    enter_indices.insert(0, 0)
                if conflict_mask[-1]:
                    exit_indices.append(len(conflict_mask) - 1)

                segments = []
                for enter_idx, exit_idx in zip(enter_indices, exit_indices):
                    segments.append(
                        {
                            "start": path[enter_idx],  # 完整的点信息
                            "end": path[exit_idx + 1],  # 完整的点信息
                            "start_idx": enter_idx,  # 保存原始索引
                            "end_idx": exit_idx + 1,  # 保存原始索引
                            "points": exit_idx - enter_idx + 2,  # 点数
                        }
                    )

                conflicts[agent_id] = {
                    "segments": segments,
                    "total_points": sum(conflict_mask),
                }

        return conflicts

    def add_solid_cylindrical_no_fly_zone(
        self,
        center_lat: float,
        center_lon: float,
        radius_meters: float,
        zone_name: str,
        planned_paths=None,
        buffer_distance_meters: Optional[int] = None,  # 改为可选，默认None
    ) -> tuple:
        """
        将圆柱状禁飞区的整个体积添加到地图中

        Args:
            center_lat: 禁飞区中心点纬度（-90到90之间）
            center_lon: 禁飞区中心点经度（-180到180之间）
            radius_meters: 禁飞区半径（米，必须为正数）
            zone_name: 禁飞区名称，不能为空
            planned_paths: 计划路径字典，用于检查冲突
            buffer_distance_meters: 安全缓冲区距离（米），用于向外扩展禁飞区，默认为0

        Returns:
            tuple: (添加的障碍物点数量, 冲突的航班ID列表)

        Raises:
            ValueError: 当输入参数无效时抛出异常
        """
        # 如果未提供缓冲区距离，从配置获取默认值
        if buffer_distance_meters is None:
            buffer_distance_meters = (
                settings.settings.map.buffer_distance_meters
            )  # Corrected access

        # 验证经纬度
        if not isinstance(center_lat, (int, float)) or not -90 <= center_lat <= 90:
            raise ValueError(f"纬度值无效: {center_lat}，应在-90到90之间")
        if not isinstance(center_lon, (int, float)) or not -180 <= center_lon <= 180:
            raise ValueError(f"经度值无效: {center_lon}，应在-180到180之间")
        if not isinstance(radius_meters, (int, float)) or radius_meters <= 0:
            raise ValueError(f"半径值无效: {radius_meters}，应为正数")

        # 注册新的禁飞区类型（如果不存在）
        try:
            self.obstacle_manager.register_type(
                zone_name, f"圆柱状禁飞区 - {center_lat},{center_lon}"
            )
        except ValueError:
            # raise ValueError(f"禁飞区名称'{zone_name}'已存在")
            logger.error(f"禁飞区名称'{zone_name}'已存在")
            return 0, {}

        # 坐标转换
        center_y, center_x = self.grid_converter.geo_to_relative(center_lat, center_lon)

        # center_x -= 5  # 地图有偏差，左移50个网格
        # center_y -= 50

        # 半径转换（米到网格单位）
        meters_per_grid = 111320 * self.grid_converter.grid_size["lat"]
        radius_in_grids = int(
            (radius_meters + buffer_distance_meters) / meters_per_grid
        )

        # print(
        #     f"禁飞区坐标转换: ({center_lat}, {center_lon}) -> 网格({center_y}, {center_x})"
        # )
        # print(f"半径转换: {radius_meters}米 -> {radius_in_grids}网格")

        # 调用网格版本的函数
        return self.add_solid_cylindrical_no_fly_zone_grid(
            int(round(center_y)),
            int(round(center_x)),
            radius_in_grids,
            zone_name,
            planned_paths,
            boundary_only=True,  # 默认只生成边界
        )

    def add_solid_cylindrical_no_fly_zone_grid(
        self,
        center_y,
        center_x,
        radius_in_grids,
        zone_name: str,
        planned_paths=None,
        max_height=None,
        boundary_only=True,  # 是否只生成边界
    ):
        """
        将圆柱状禁飞区的整个体积添加到地图中

        Args:
            center_y: 禁飞区中心点Y坐标
            center_x: 禁飞区中心点X坐标
            radius_in_grids: 禁飞区半径（网格单位）
            zone_name: 禁飞区名称
            planned_paths: 计划路径字典，用于检查冲突
            max_height: 最大高度，默认为地图深度
            boundary_only: 是否只生成边界，默认为True

        Returns:
            tuple: (添加的障碍物点数量, 冲突的航班ID列表)
        """
        # 注册障碍物类型
        # self.obstacle_manager.register_type(zone_name, "no_fly")

        # 设置最大高度
        max_height = max_height or self.depth

        if boundary_only:
            # 只生成边界点
            # 使用配置中的边界厚度
            obstacle_points = generate_cylinder_boundary_points_3d(
                (center_y, center_x),
                radius_in_grids,
                max_height,
                boundary_thickness=settings.settings.map.boundary_thickness,  # 直接从settings获取 # Corrected access
            )
            # logger.info(f"生成了 {len(obstacle_points)} 个圆柱体边界点")
        else:
            # 计算边界框范围
            y_min = max(0, center_y - radius_in_grids)
            y_max = min(self.height, center_y + radius_in_grids)
            x_min = max(0, center_x - radius_in_grids)
            x_max = min(self.width, center_x + radius_in_grids)
            # 创建坐标网格
            y_coords, x_coords = np.meshgrid(
                np.arange(y_min, y_max), np.arange(x_min, x_max), indexing="ij"
            )

            # 计算每个点到中心的距离
            distances = (y_coords - center_y) ** 2 + (x_coords - center_x) ** 2

            # 找到在圆柱体内的点
            mask = distances <= radius_in_grids**2

            # 获取有效点的坐标
            valid_y = y_coords[mask]
            valid_x = x_coords[mask]

            if len(valid_y) > 0:
                # 首先将坐标转换为整数
                valid_y_int = valid_y.astype(int)
                valid_x_int = valid_x.astype(int)

                # 直接生成点集合
                obstacle_points = set()
                for z in range(max_height):
                    for i in range(len(valid_y_int)):
                        obstacle_points.add(
                            (int(valid_y_int[i]), int(valid_x_int[i]), z)
                        )

                # logger.info(f"生成了 {len(obstacle_points)} 个障碍物点")
            else:
                obstacle_points = set()

        # elapsed = time.time() - start_time
        # logger.info(f"生成点集合总耗时: {elapsed:.2f}秒")

        # 批量更新障碍物管理器
        self.obstacle_manager.add_positions_batch(zone_name, obstacle_points)

        # 更新不可通行点集合
        self.non_traversable.update(obstacle_points)

        # 存储禁飞区 2D 几何定义以供起点检查
        self.nfz_definitions[zone_name] = {
            "type": "cylinder",
            "center_yx": (center_y, center_x),
            "radius_sq": radius_in_grids**2,
            # 'max_height': max_height # 2D检查不需要高度
        }

        # 注意：圆柱体不需要添加到多边形索引中

        total_points = len(obstacle_points)
        logger.info(
            f"已将实心圆柱状禁飞区 {zone_name} 添加到地图中, 中心点：({center_y}, {center_x})，半径：{radius_in_grids}网格，使用{total_points}个障碍物点"
        )  # 生成轨道点序和象限分类并保存到obstacle_manager中
        orbit_points, quadrants = self._generate_cylindrical_orbit_path(
            center_y, center_x, radius_in_grids
        )
        self.obstacle_manager.set_orbit_path(zone_name, orbit_points)
        self.obstacle_manager.set_orbit_quadrants(zone_name, quadrants)

        logger.info(
            f"已为禁飞区 {zone_name} 生成 {len(orbit_points)} 个轨道点，分为8个象限"
        )
        for quad_name, indices in quadrants.items():
            logger.info(f"  象限 {quad_name}: {len(indices)} 个点")

        # 检查冲突，直接使用障碍物点集合
        conflicts = self.check_pre_flight_conflicts(obstacle_points, planned_paths)

        return total_points, conflicts

    def add_solid_polygon_no_fly_zone(
        self,
        coords: list,
        zone_name: str,
        planned_paths=None,
        buffer_distance_meters: Optional[int] = None,  # 改为可选，默认None
    ) -> tuple:
        """
        将多边形柱体禁飞区的整个体积添加到地图中

        Args:
            coords: 多边形顶点坐标列表，每个元素是[lon, lat]格式
            zone_name: 禁飞区名称，不能为空
            planned_paths: 已规划的路径字典
            buffer_distance_meters: 安全缓冲区距离（米），用于向外扩展多边形，默认为0

        Returns:
            tuple: (添加的障碍物点数量, 冲突的航班ID列表)

        Raises:
            ValueError: 当输入参数无效时抛出异常
        """
        # 如果未提供缓冲区距离，从配置获取默认值
        if buffer_distance_meters is None:
            buffer_distance_meters = (
                settings.settings.map.buffer_distance_meters
            )  # Corrected access

        # 验证多边形坐标
        if not isinstance(coords, list) or len(coords) < 3:
            raise ValueError("coords必须是包含至少3个顶点的列表")

        # 注册新的禁飞区类型（如果不存在）
        try:
            self.obstacle_manager.register_type(
                zone_name, f"多边形禁飞区 - {len(coords)}个顶点"
            )
        except ValueError:
            raise ValueError(f"禁飞区名称'{zone_name}'已存在")

        # 将所有顶点的经纬度转换为网格坐标
        grid_points = np.array(
            [
                self.grid_converter.geo_to_relative(point["lat"], point["lng"])
                for point in coords
            ],
            dtype=np.float32,
        )

        # grid_points[:, 0] -= 5  # 地图有偏差，左移50个网格
        # grid_points[:, 1] -= 50

        # 将安全缓冲区距离从米转换为网格单位
        expand_distance_grids = 0
        if buffer_distance_meters > 0:
            meters_per_grid = 111320 * self.grid_converter.grid_size["lat"]
            expand_distance_grids = int(buffer_distance_meters / meters_per_grid)
            # logger.info(
            #     f"安全缓冲区距离: {buffer_distance_meters}米 -> {expand_distance_grids}网格"
            # )

        # 调用网格版本的函数
        return self.add_solid_polygon_no_fly_zone_grid(
            grid_points,
            zone_name,
            planned_paths,
            expand_distance_grids=expand_distance_grids,
            boundary_only=True,  # 默认只生成边界
        )

    def _ensure_clockwise_order(self, polygon: np.ndarray) -> np.ndarray:
        """确保多边形顶点是顺时针顺序"""
        # 计算多边形的符号面积
        area = 0
        for i in range(len(polygon)):
            j = (i + 1) % len(polygon)
            area += polygon[i, 0] * polygon[j, 1]
            area -= polygon[j, 0] * polygon[i, 1]
        area = area / 2.0

        # 如果面积为正（逆时针），翻转顶点顺序
        if area > 0:
            return polygon[::-1]
        return polygon

    def _expand_polygon(self, polygon: np.ndarray, expand_distance: int) -> np.ndarray:
        """
        将多边形向外扩展指定的距离，用于创建安全缓冲区

        Args:
            polygon: 多边形顶点坐标数组，每行是[y, x]格式
            expand_distance: 安全缓冲区距离（网格单位）

        Returns:
            np.ndarray: 扩展后的多边形顶点坐标数组
        """
        # 首先确保顺时针顺序
        polygon = self._ensure_clockwise_order(polygon)

        if expand_distance <= 0:
            return polygon

        # 获取多边形顶点数量
        n_vertices = len(polygon)

        # 计算多边形的面积，用于确定多边形的旋转方向
        area = 0
        for i in range(n_vertices):
            j = (i + 1) % n_vertices
            area += polygon[i, 0] * polygon[j, 1]
            area -= polygon[j, 0] * polygon[i, 1]
        area = area / 2.0

        # 确定法线方向的符号，保证向外扩展
        # 如果面积为正（逆时针），法线方向为(dy, -dx)
        # 如果面积为负（顺时针），法线方向为(-dy, dx)
        normal_sign = -1 if area > 0 else 1

        # 创建扩展后的多边形
        expanded_polygon = []

        # 对每个顶点，计算其法线并沿法线方向扩展
        for i in range(n_vertices):
            # 获取当前顶点及其相邻顶点
            prev_idx = (i - 1) % n_vertices
            curr_idx = i
            next_idx = (i + 1) % n_vertices

            prev_point = polygon[prev_idx]
            curr_point = polygon[curr_idx]
            next_point = polygon[next_idx]

            # 计算当前顶点的两个相邻边的向量
            prev_edge = curr_point - prev_point
            next_edge = next_point - curr_point

            # 将向量归一化
            prev_edge_length = np.sqrt(np.sum(prev_edge**2))
            next_edge_length = np.sqrt(np.sum(next_edge**2))

            if prev_edge_length > 0 and next_edge_length > 0:
                prev_edge_normalized = prev_edge / prev_edge_length
                next_edge_normalized = next_edge / next_edge_length

                # 计算两个边的法线（垂直于边的向量）
                # 根据多边形的旋转方向确定法线方向
                prev_normal = np.array(
                    [
                        normal_sign * -prev_edge_normalized[1],
                        normal_sign * prev_edge_normalized[0],
                    ]
                )
                next_normal = np.array(
                    [
                        normal_sign * -next_edge_normalized[1],
                        normal_sign * next_edge_normalized[0],
                    ]
                )

                # 将两个法线向量相加并归一化，得到角平分线方向
                bisector = prev_normal + next_normal
                bisector_length = np.sqrt(np.sum(bisector**2))

                if bisector_length > 0:
                    bisector_normalized = bisector / bisector_length

                    # 计算角平分线与法线之间的角度
                    # 使用点积计算余弦值
                    cos_angle = np.dot(bisector_normalized, prev_normal)
                    # 防止除以0
                    if abs(cos_angle) < 1e-10:
                        cos_angle = 1e-10

                    # 计算扩展距离（考虑角度因素）
                    # 当角度越小，需要的扩展距离越大
                    adjusted_distance = expand_distance / abs(cos_angle)
                    # 限制最大扩展距离，防止在尖角处扩展过大
                    adjusted_distance = min(adjusted_distance, expand_distance * 3)

                    # 沿角平分线方向扩展顶点
                    expanded_point = (
                        curr_point + bisector_normalized * adjusted_distance
                    )
                    expanded_polygon.append(expanded_point)
                else:
                    # 如果无法计算角平分线，使用简单的向外扩展
                    # 计算多边形的中心点
                    center = np.mean(polygon, axis=0)
                    vector = curr_point - center
                    vector_length = np.sqrt(np.sum(vector**2))
                    if vector_length > 0:
                        expanded_point = (
                            curr_point + (vector / vector_length) * expand_distance
                        )
                        expanded_polygon.append(expanded_point)
                    else:
                        expanded_polygon.append(curr_point)
            else:
                # 如果边长度为0，使用简单的向外扩展
                # 计算多边形的中心点
                center = np.mean(polygon, axis=0)
                vector = curr_point - center
                vector_length = np.sqrt(np.sum(vector**2))
                if vector_length > 0:
                    expanded_point = (
                        curr_point + (vector / vector_length) * expand_distance
                    )
                    expanded_polygon.append(expanded_point)
                else:
                    expanded_polygon.append(curr_point)

        return np.array(expanded_polygon)

    def add_solid_polygon_no_fly_zone_grid(
        self,
        grid_points: np.ndarray,
        zone_name: str,
        planned_paths=None,
        max_height=None,
        expand_distance_grids=0,  # 安全缓冲区距离（网格单位）
        boundary_only=True,  # 是否只生成边界
    ) -> tuple:
        """
        将多边形柱体禁飞区的整个体积添加到地图中（网格坐标版本）

        Args:
            grid_points: 多边形顶点的网格坐标数组，nx2数组，每行是[y, x]格式
            zone_name: 禁飞区名称
            planned_paths: 已规划的路径字典
            max_height: 最大高度，默认为地图深度
            expand_distance_grids: 安全缓冲区距离（网格单位），用于向外扩展多边形
            boundary_only: 是否只生成边界，默认为True

        Returns:
            tuple: (添加的障碍物点数量, 冲突的航班ID列表)
        """
        # 注册障碍物类型
        # self.obstacle_manager.register_type(zone_name, "no_fly")

        # 设置最大高度
        max_height = max_height or self.depth

        # 如果指定了安全缓冲区距离，扩展多边形
        polygon = grid_points[:, :2]  # 只取y,x坐标
        if expand_distance_grids > 0:
            polygon = self._expand_polygon(polygon, expand_distance_grids)

        if boundary_only:
            # 只生成边界点
            # 使用配置中的边界厚度，而不是缓冲区距离
            obstacle_points = generate_polygon_boundary_points_3d(
                polygon,
                max_height,
                boundary_thickness=settings.settings.map.boundary_thickness,  # 直接从settings获取 # Corrected access
            )
            # logger.info(f"生成了 {len(obstacle_points)} 个多边形边界点")
        else:
            # 计算边界框范围
            y_min = np.floor(np.min(polygon[:, 0]))
            y_max = np.ceil(np.max(polygon[:, 0]))
            x_min = np.floor(np.min(polygon[:, 1]))
            x_max = np.ceil(np.max(polygon[:, 1]))

            # 确保边界在地图范围内
            y_min = max(0, int(y_min))
            y_max = min(self.height, int(y_max) + 1)
            x_min = max(0, int(x_min))
            x_max = min(self.width, int(x_max) + 1)

            # 创建网格点
            y_coords, x_coords = np.meshgrid(
                np.arange(y_min, y_max), np.arange(x_min, x_max), indexing="ij"
            )

            # 准备用于判断的点
            points = np.column_stack((y_coords.ravel(), x_coords.ravel()))

            # 使用完全向量化的点在多边形内检查算法
            from .optimized_polygon_check_v2 import points_in_polygon_vectorized

            mask = points_in_polygon_vectorized(points, polygon)

            # 重塑mask以匹配原始网格形状
            mask = mask.reshape(y_coords.shape)

            if np.any(mask):
                # 获取有效点的坐标
                valid_y = y_coords[mask]
                valid_x = x_coords[mask]

                # 首先将坐标转换为整数
                valid_y_int = valid_y.astype(int)
                valid_x_int = valid_x.astype(int)

                # 直接生成点集合
                obstacle_points = set()
                for z in range(max_height):
                    for i in range(len(valid_y_int)):
                        obstacle_points.add(
                            (int(valid_y_int[i]), int(valid_x_int[i]), z)
                        )

                # logger.info(f"生成了 {len(obstacle_points)} 个障碍物点")
            else:
                obstacle_points = set()

        # 计时代码已移除

        # 批量更新障碍物管理器
        self.obstacle_manager.add_positions_batch(zone_name, obstacle_points)

        # 更新不可通行点集合
        self.non_traversable.update(obstacle_points)

        # 存储禁飞区 2D 几何定义以供起点检查
        self.nfz_definitions[zone_name] = {
            "type": "polygon",
            "vertices_yx": polygon,  # 存储多边形顶点
        }

        # 将多边形添加到空间索引中以加速点检测
        self.polygon_index.add_polygon(zone_name, polygon)

        total_points = len(obstacle_points)
        if boundary_only:
            logger.info(
                f"已将多边形柱体禁飞区{zone_name}的边界添加到地图中，使用{total_points}个边界点"
            )
        else:
            logger.info(
                f"已将多边形柱体禁飞区{zone_name}添加到地图中，使用{total_points}个障碍物点"
            )

        # 生成轨道点序和象限分类并保存到obstacle_manager中
        orbit_points, quadrants = self._generate_polygon_orbit_path(polygon)
        self.obstacle_manager.set_orbit_path(zone_name, orbit_points)
        self.obstacle_manager.set_orbit_quadrants(zone_name, quadrants)

        logger.info(
            f"已为禁飞区 {zone_name} 生成 {len(orbit_points)} 个轨道点，分为8个象限"
        )
        for quad_name, indices in quadrants.items():
            logger.info(f"  象限 {quad_name}: {len(indices)} 个点")

        # 检查冲突，直接使用障碍物点集合
        conflicts = self.check_pre_flight_conflicts(obstacle_points, planned_paths)

        return total_points, conflicts

    def check_path_conflicts_with_obstacles(self, path):
        """
        检查路径是否与地图中的任何障碍物冲突

        Args:
            path: 路径点列表，每个点包含lat, lng, height, time属性
            begin_time: 航班的开始时间戳

        Returns:
            tuple: (start_conflict_types, end_conflict_types, current_node_index, first_conflict)
                   start_conflict_types: 起点冲突的障碍物类型列表
                   end_conflict_types: 终点冲突的障碍物类型列表
                   current_node_index: 当前无人机所在节点的索引
                   first_conflict: 字典，包含第一个冲突点的信息，格式：
                                 {'index': 节点索引, 'position': (y,x,z), 'types': [障碍物类型列表]}
                                 如果没有冲突则为None
        """
        import time

        current_time = time.time()
        current_node_index = -1
        found_current_node = False
        first_conflict = None

        # 检查起点和终点
        start_point = path[0]
        end_point = path[-1]

        # 转换起点坐标
        start_y, start_x, start_z = self.grid_converter.geo_to_relative(
            start_point["lat"], start_point["lng"], start_point["height"]
        )
        start_pos = (int(start_y), int(start_x), int(start_z))

        # 转换终点坐标
        end_y, end_x, end_z = self.grid_converter.geo_to_relative(
            end_point["lat"], end_point["lng"], end_point["height"]
        )
        end_pos = (int(end_y), int(end_x), int(end_z))

        # 获取起点和终点的冲突类型
        start_conflict_types = self.obstacle_manager.get_type_at_position(start_pos)
        end_conflict_types = self.obstacle_manager.get_type_at_position(end_pos)

        # 检查路径上的每个点
        for index, point in enumerate(path):
            if not found_current_node and point["t"] <= current_time:
                current_node_index = index
            elif point["t"] > current_time:
                found_current_node = True

            y, x, z = self.grid_converter.geo_to_relative(
                point["lat"], point["lng"], point["height"]
            )
            pos = (int(y), int(x), int(z))

            if not self.traversable(pos[0], pos[1], pos[2]):
                conflict_types = self.obstacle_manager.get_type_at_position(pos)
                if conflict_types:  # 如果有冲突类型
                    first_conflict = {
                        "index": index,
                        "position": pos,
                        "types": conflict_types,
                    }
                    break

        return (
            start_conflict_types,
            end_conflict_types,
            current_node_index,
            first_conflict,
        )

    def check_pre_flight_conflicts(
        self, obstacle_points: set, planned_paths: dict = None
    ) -> List[str]:
        """
        检查未起飞航班的路径是否与禁飞区冲突

        Args:
            obstacle_points: 禁飞区的点集合，每个元素是(y, x, z)元组
            planned_paths: 路径信息字典 {flight_id: flight_info}

        Returns:
            List[str]: 与禁飞区冲突的航班ID列表
        """
        conflicts = []
        if not planned_paths:
            return conflicts

        # 遍历所有计划路径
        for agent_id, flight_info in planned_paths.items():
            # 只处理未起飞的航班
            source = flight_info.get("source")
            if source not in ["待执行"]:
                continue

            grid_path_nodes = flight_info.get("path_nodes", [])
            grid_path = [(node.y, node.x, node.z) for node in grid_path_nodes]
            if not grid_path:
                continue

            # 检查路径是否与禁飞区相交（使用集合交集操作，更高效）
            path_set = set(grid_path)
            if path_set.intersection(obstacle_points):
                conflicts.append({"flight_id": agent_id, "source": source})

        return conflicts

    def remove_no_fly_zone(self, zone_name: str) -> bool:
        """
        删除指定名称的禁飞区

        Args:
            zone_name: 要删除的禁飞区名称

        Returns:
            bool: 如果成功删除返回True，如果禁飞区类型不存在返回False
        """
        try:
            # 获取要删除的禁飞区的位置信息
            freed_positions = self.obstacle_manager.get_positions(zone_name)
            if not freed_positions:
                return True

            # 获取其他所有障碍物类型
            other_zones = set(self.obstacle_manager.get_all_types().keys()) - {
                zone_name
            }  # 注销要删除的类型
            self.obstacle_manager.unregister_type(zone_name)

            # 同时从几何定义字典中移除
            definition = self.nfz_definitions.pop(zone_name, None)

            # 移除轨道点序
            self.obstacle_manager.remove_orbit_path(zone_name)

            # 如果是多边形类型，从多边形索引中移除
            if definition and definition.get("type") == "polygon":
                self.polygon_index.remove_polygon(zone_name)

            # 预先计算所有其他障碍物的位置集合（并集）
            all_other_obstacles = set()
            for zone in other_zones:
                all_other_obstacles.update(self.obstacle_manager.get_positions(zone))

            # 计算真正可以释放的点（不被其他障碍物占用的点）
            truly_freed = freed_positions - all_other_obstacles

            # 从不可通行点集合中移除这些点
            self.non_traversable.difference_update(truly_freed)

            return True
        except ValueError:
            return False

    def is_points_in_polygon_batch(
        self, points_yx: List[Tuple[int, int]], polygon_id: str
    ) -> List[bool]:
        """
        批量检查多个点是否在指定多边形内部

        Args:
            points_yx: 要检查的点的 (y, x) 网格坐标列表
            polygon_id: 多边形ID

        Returns:
            List[bool]: 与输入点等长的布尔值列表，表示每个点是否在多边形内部
        """
        # 获取多边形顶点
        polygon = self.polygon_index.polygons.get(polygon_id)
        if polygon is None:
            return [False] * len(points_yx)

        # 将点转换为numpy数组
        points_array = np.array(points_yx)

        # 使用向量化的点在多边形内检测函数
        return points_in_polygon_vectorized(points_array, polygon).tolist()

    def is_point_inside_any_nfz_2d(self, point_yx: Tuple[int, int]) -> Optional[str]:
        """
        检查给定的2D点是否位于任何已定义的禁飞区（NFZ）的2D投影内部。
        使用空间索引和缓存优化性能。
        对于多边形禁飞区，先检查点是否在外接矩形内，然后进行精确的多边形内部检测。

        Args:
            point_yx: 要检查的点的 (y, x) 网格坐标。

        Returns:
            如果点位于任何NFZ内部，则返回该NFZ的名称 (str)。
            如果点不在任何NFZ内部，则返回 None。
        """
        point_y, point_x = point_yx

        # 首先使用多边形索引的外接矩形快速检查
        # 然后对候选多边形进行精确的点在多边形内检测
        # 首先使用外接矩形进行快速筛选
        candidate_zones = []
        for zone_name, bounds in self.polygon_index.bounds.items():
            y_min, y_max, x_min, x_max = bounds
            if y_min <= point_y <= y_max and x_min <= point_x <= x_max:
                # 点在外接矩形内，将该区域添加为候选区域
                candidate_zones.append(zone_name)

        # 如果有候选区域，进行精确的点在多边形内检测
        if candidate_zones:
            for zone_name in candidate_zones:
                polygon = self.polygon_index.polygons.get(zone_name)
                if polygon is not None and is_point_in_polygon_precise(
                    (point_y, point_x), polygon
                ):
                    return zone_name

        # 然后检查圆柱体禁飞区
        for zone_name, definition in self.nfz_definitions.items():
            nfz_type = definition.get("type")

            if nfz_type == "cylinder":
                center_y, center_x = definition.get("center_yx", (None, None))
                radius_sq = definition.get("radius_sq", -1)
                if center_y is not None and radius_sq >= 0:
                    # 检查点是否在圆内（使用平方距离）
                    dist_sq = (point_y - center_y) ** 2 + (point_x - center_x) ** 2
                    if dist_sq <= radius_sq:
                        return zone_name  # 点在圆柱投影内

            # 多边形类型已经由外接矩形检查处理

            # 可以扩展以支持其他类型的禁飞区

        return None  # 点不在任何已定义的禁飞区内

    def is_point_in_polygon_precise(
        self, point_yx: Tuple[int, int], polygon_id: str
    ) -> bool:
        """
        精确判断点是否在指定多边形内部

        使用射线投射算法（Ray Casting Algorithm）的优化版本，比简单的外接矩形检查更精确。

        Args:
            point_yx: 要检查的点的 (y, x) 网格坐标
            polygon_id: 多边形ID

        Returns:
            bool: 如果点在多边形内部返回True，否则返回False
        """
        # 获取多边形顶点
        polygon = self.polygon_index.polygons.get(polygon_id)
        if polygon is None:
            return False

        # 使用优化的精确判断函数
        return is_point_in_polygon_precise(point_yx, polygon)

    def check_points_in_polygons(
        self, points_yx: List[Tuple[int, int]]
    ) -> Dict[str, List[bool]]:
        """
        检查一组点是否在每个多边形内

        这个函数对每个多边形，返回每个点是否在该多边形内的布尔值列表

        Args:
            points_yx: 要检查的点的 (y, x) 网格坐标列表

        Returns:
            Dict[str, List[bool]]: 字典，键为多边形ID，值为布尔值列表，表示每个点是否在该多边形内
        """
        results = {}

        # 如果没有点或没有多边形，直接返回空字典
        if not points_yx or not self.polygon_index.polygons:
            return results

        # 将点转换为numpy数组以提高性能
        points_array = np.array(points_yx)

        # 对每个多边形进行检查
        for polygon_id, polygon in self.polygon_index.polygons.items():
            # 使用向量化的点在多边形内检测函数
            results[polygon_id] = points_in_polygon_vectorized(
                points_array, polygon
            ).tolist()

        return results

    def is_points_inside_any_nfz_2d_batch(
        self, points_yx: List[Tuple[int, int]]
    ) -> List[Optional[str]]:
        """
        批量检查多个2D点是否位于任何已定义的禁飞区（NFZ）的2D投影内部。
        对于多边形禁飞区，先检查点是否在外接矩形内，再进行精确的多边形内部检测，以提高性能。

        Args:
            points_yx: 要检查的点的 (y, x) 网格坐标列表。

        Returns:
            与输入点等长的列表，包含每个点所在的NFZ名称，如果点不在任何NFZ内则为None。
        """
        # 初始化结果列表
        results = [None] * len(points_yx)

        # 对于每个点，先检查是否在任何多边形的外接矩形内
        for i, point in enumerate(points_yx):
            point_y, point_x = point

            # 首先使用外接矩形进行快速筛选
            candidate_zones = []
            for zone_name, bounds in self.polygon_index.bounds.items():
                y_min, y_max, x_min, x_max = bounds
                if y_min <= point_y <= y_max and x_min <= point_x <= x_max:
                    # 点在外接矩形内，将该区域添加为候选区域
                    candidate_zones.append(zone_name)

            # 如果有候选区域，进行精确的点在多边形内检测
            if candidate_zones:
                for zone_name in candidate_zones:
                    polygon = self.polygon_index.polygons.get(zone_name)
                    if polygon is not None and is_point_in_polygon_precise(
                        (point_y, point_x), polygon
                    ):
                        results[i] = zone_name
                        break

            # 如果点不在任何多边形的外接矩形内，检查圆柱体
            if results[i] is None:
                # 检查圆柱体禁飞区
                for zone_name, definition in self.nfz_definitions.items():
                    nfz_type = definition.get("type")

                    if nfz_type == "cylinder":
                        center_y, center_x = definition.get("center_yx", (None, None))
                        radius_sq = definition.get("radius_sq", -1)
                        if center_y is not None and radius_sq >= 0:
                            # 检查点是否在圆内（使用平方距离）
                            dist_sq = (point_y - center_y) ** 2 + (
                                point_x - center_x
                            ) ** 2
                            if dist_sq <= radius_sq:
                                results[i] = zone_name  # 点在圆柱投影内
                                break

        return results

    def _generate_cylindrical_orbit_path(
        self,
        center_y: int,
        center_x: int,
        radius_in_grids: int,
        buffer_distance: int = 1,
        num_points: int = None,
    ) -> Tuple[List[Tuple[int, int]], Dict[str, Set[int]]]:
        """
        生成圆柱状禁飞区外围紧贴的有序轨道点序（二维）并同时进行8个象限分类

        Args:
            center_y: 圆柱体中心Y坐标
            center_x: 圆柱体中心X坐标
            radius_in_grids: 圆柱体半径（网格单位）
            buffer_distance: 与禁飞区边界的缓冲距离（网格单位），默认1
            num_points: 轨道点数量，如果为None则根据周长自动计算

        Returns:
            Tuple[List[Tuple[int, int]], Dict[str, Set[int]]]:
                (有序的轨道点序列，象限分类字典)
        """
        # 计算轨道半径（禁飞区半径 + 缓冲距离）
        orbit_radius = radius_in_grids + buffer_distance

        # 如果未指定点数，根据轨道周长自动计算，确保点足够密集
        if num_points is None:
            # 周长
            circumference = 2 * math.pi * orbit_radius
            # 每个网格单位大约放置2个点，确保轨道密集
            num_points = max(16, int(circumference * 1))

        orbit_points = []

        # 初始化8个象限
        quadrants = {
            "N": set(),  # 正北
            "NE": set(),  # 东北
            "E": set(),  # 正东
            "SE": set(),  # 东南
            "S": set(),  # 正南
            "SW": set(),  # 西南
            "W": set(),  # 正西
            "NW": set(),  # 西北
        }

        # 生成圆形轨道上的点并同时进行象限分类
        valid_point_index = 0  # 有效点的索引计数器
        for i in range(num_points):
            # 角度（从0到2π）
            angle = 2 * math.pi * i / num_points

            # 计算轨道点坐标
            orbit_y = center_y + orbit_radius * math.cos(angle)
            orbit_x = center_x + orbit_radius * math.sin(angle)

            # 转换为整数网格坐标
            grid_y = int(round(orbit_y))
            grid_x = int(round(orbit_x))

            # 检查坐标是否在地图范围内（放宽检查，允许负坐标）
            if self.in_bounds(grid_y, grid_x, 0):  # 只检查2D边界:
                orbit_points.append((grid_y, grid_x))

                # 计算角度并分配到对应象限
                # 计算相对于中心点的坐标
                dy = grid_y - center_y
                dx = grid_x - center_x

                # 计算角度（弧度），然后转换为度数
                # atan2 返回 (-π, π]，我们需要转换为 [0, 360)
                angle_rad = math.atan2(dx, dy)  # Y轴向下为正，与数学坐标系相同
                angle_deg = math.degrees(angle_rad)
                if angle_deg < 0:
                    angle_deg += 360

                # 根据数学坐标系的标准，0度指向正东
                if 337.5 <= angle_deg or angle_deg < 22.5:
                    quadrants["E"].add(valid_point_index)  # 0°指向东
                elif 22.5 <= angle_deg < 67.5:
                    quadrants["NE"].add(valid_point_index)
                elif 67.5 <= angle_deg < 112.5:
                    quadrants["N"].add(valid_point_index)  # 90°指向北
                elif 112.5 <= angle_deg < 157.5:
                    quadrants["NW"].add(valid_point_index)
                elif 157.5 <= angle_deg < 202.5:
                    quadrants["W"].add(valid_point_index)  # 180°指向西
                elif 202.5 <= angle_deg < 247.5:
                    quadrants["SW"].add(valid_point_index)
                elif 247.5 <= angle_deg < 292.5:
                    quadrants["S"].add(valid_point_index)  # 270°指向南
                elif 292.5 <= angle_deg < 337.5:
                    quadrants["SE"].add(valid_point_index)

                valid_point_index += 1

        return orbit_points, quadrants

    def get_orbit_path(self, zone_name: str) -> List[Tuple[int, int]]:
        """
        获取禁飞区的轨道点序

        Args:
            zone_name: 禁飞区名称

        Returns:
            List[Tuple[int, int]]: 有序的轨道点序列，如果不存在则返回空列表
        """
        return self.obstacle_manager.get_orbit_path(zone_name)

    def remove_orbit_path(self, zone_name: str) -> List[Tuple[int, int]]:
        """
        移除禁飞区的轨道点序

        Args:
            zone_name: 禁飞区名称

        Returns:
            List[Tuple[int, int]]: 被移除的轨道点序列，如果不存在则返回空列表
        """
        return self.obstacle_manager.remove_orbit_path(zone_name)

    def get_orbit_quadrants(self, zone_name: str) -> Dict[str, Set[int]]:
        """
        获取禁飞区轨道点序的8个象限分类

        Args:
            zone_name: 禁飞区名称

        Returns:
            Dict[str, Set[int]]: 象限字典，如果不存在则返回空字典
        """
        return self.obstacle_manager.get_orbit_quadrants(zone_name)

    def get_quadrant_points(
        self, zone_name: str, quadrant_name: str
    ) -> List[Tuple[int, int]]:
        """
        获取指定象限的轨道点坐标

        Args:
            zone_name: 禁飞区名称
            quadrant_name: 象限名称 (N, NE, E, SE, S, SW, W, NW)

        Returns:
            List[Tuple[int, int]]: 该象限的轨道点坐标列表
        """
        return self.obstacle_manager.get_quadrant_points(zone_name, quadrant_name)

    def _generate_polygon_orbit_path(
        self,
        polygon: np.ndarray,
        buffer_distance: int = 1,
        num_points_per_edge: int = None,
    ) -> Tuple[List[Tuple[int, int]], Dict[str, Set[int]]]:
        """
        生成多边形禁飞区外围紧贴的有序轨道点序（二维）并同时进行8个象限分类

        Args:
            polygon: 多边形顶点坐标数组，每行是[y, x]格式
            buffer_distance: 与禁飞区边界的缓冲距离（网格单位），默认1
            num_points_per_edge: 每条边的点数，如果为None则根据边长自动计算

        Returns:
            Tuple[List[Tuple[int, int]], Dict[str, Set[int]]]:
                (有序的轨道点序列，象限分类字典)
        """
        # 计算多边形的中心点（用于象限分类）
        center_y = np.mean(polygon[:, 0])
        center_x = np.mean(polygon[:, 1])

        # 扩展多边形以创建轨道路径
        expanded_polygon = self._expand_polygon(polygon, buffer_distance)

        orbit_points = []

        # 初始化8个象限
        quadrants = {
            "N": set(),  # 正北
            "NE": set(),  # 东北
            "E": set(),  # 正东
            "SE": set(),  # 东南
            "S": set(),  # 正南
            "SW": set(),  # 西南
            "W": set(),  # 正西
            "NW": set(),  # 西北
        }

        # 沿着扩展后的多边形边界生成轨道点
        n_vertices = len(expanded_polygon)
        valid_point_index = 0  # 有效点的索引计数器

        for i in range(n_vertices):
            # 当前边的起点和终点
            start_point = expanded_polygon[i]
            end_point = expanded_polygon[(i + 1) % n_vertices]

            # 计算边长
            edge_length = np.sqrt(np.sum((end_point - start_point) ** 2))

            # 如果未指定每条边的点数，根据边长自动计算
            if num_points_per_edge is None:
                # 每个网格单位大约放置1个点，最少2个点
                points_on_edge = max(2, int(edge_length))
            else:
                points_on_edge = num_points_per_edge

            # 在当前边上生成点（不包括终点，避免重复）
            for j in range(points_on_edge):
                t = j / points_on_edge  # 参数化位置 [0, 1)

                # 线性插值计算点坐标
                point_y = start_point[0] + t * (end_point[0] - start_point[0])
                point_x = start_point[1] + t * (end_point[1] - start_point[1])

                # 转换为整数网格坐标
                grid_y = int(round(point_y))
                grid_x = int(round(point_x))

                # 检查点是否在原始多边形外部（确保不进入禁飞区）
                if not self._is_point_in_polygon_simple((grid_y, grid_x), polygon):
                    # 检查坐标是否在地图范围内（放宽检查，允许负坐标）
                    if self.in_bounds(grid_y, grid_x, 0):
                        orbit_points.append((grid_y, grid_x))

                        # 计算角度并分配到对应象限
                        dy = grid_y - center_y
                        dx = grid_x - center_x

                        # 计算角度（弧度），然后转换为度数
                        angle_rad = math.atan2(dx, dy)  # Y轴向下为正，与数学坐标系相同
                        angle_deg = math.degrees(angle_rad)
                        if angle_deg < 0:
                            angle_deg += 360

                        # 根据数学坐标系的标准，0度指向正东
                        if 337.5 <= angle_deg or angle_deg < 22.5:
                            quadrants["E"].add(valid_point_index)  # 0°指向东
                        elif 22.5 <= angle_deg < 67.5:
                            quadrants["NE"].add(valid_point_index)
                        elif 67.5 <= angle_deg < 112.5:
                            quadrants["N"].add(valid_point_index)  # 90°指向北
                        elif 112.5 <= angle_deg < 157.5:
                            quadrants["NW"].add(valid_point_index)
                        elif 157.5 <= angle_deg < 202.5:
                            quadrants["W"].add(valid_point_index)  # 180°指向西
                        elif 202.5 <= angle_deg < 247.5:
                            quadrants["SW"].add(valid_point_index)
                        elif 247.5 <= angle_deg < 292.5:
                            quadrants["S"].add(valid_point_index)  # 270°指向南
                        elif 292.5 <= angle_deg < 337.5:
                            quadrants["SE"].add(valid_point_index)

                        valid_point_index += 1

        return orbit_points, quadrants

    def _is_point_in_polygon_simple(
        self, point_yx: Tuple[int, int], polygon: np.ndarray
    ) -> bool:
        """
        简单的点在多边形内检测（射线投射算法）

        Args:
            point_yx: 要检查的点的 (y, x) 坐标
            polygon: 多边形顶点坐标数组

        Returns:
            bool: 如果点在多边形内部返回True，否则返回False
        """
        y, x = point_yx
        n = len(polygon)
        inside = False

        p1y, p1x = polygon[0]
        for i in range(1, n + 1):
            p2y, p2x = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1y, p1x = p2y, p2x

        return inside
