import json
import os
import numpy as np
import sys
import math
from typing import Tuple, Dict

# Import MapConfig for type hinting
from ...config.settings import MapConfig
from ...utils.logging import get_logger

logger = get_logger(__name__)

# GeoHash related constants and functions remain unchanged
BASE32 = "0123456789bcdefghjkmnpqrstuvwxyz"
BITS = [16, 8, 4, 2, 1]

Neighbors = [
    [
        "p0r21436x8zb9dcf5h7kjnmqesgutwvy",  # Top
        "bc01fg45238967deuvhjyznpkmstqrwx",  # Right
        "14365h7k9dcfesgujnmqp0r2twvyx8zb",  # Bottom
        "238967debc01fg45kmstqrwxuvhjyznp",  # Left
    ],
    [
        "bc01fg45238967deuvhjyznpkmstqrwx",  # Top
        "p0r21436x8zb9dcf5h7kjnmqesgutwvy",  # Right
        "238967debc01fg45kmstqrwxuvhjyznp",  # Bottom
        "14365h7k9dcfesgujnmqp0r2twvyx8zb",  # Left
    ],
]

Borders = [
    ["prxz", "bcfguvyz", "028b", "0145hjnp"],
    ["bcfguvyz", "prxz", "0145hjnp", "028b"],
]


def Direction(enum):
    """获取方向的索引"""
    if enum == "Top":
        return 0
    if enum == "Right":
        return 1
    if enum == "Bottom":
        return 2
    if enum == "Left":
        return 3


def calculate_adjacent(geohash, direction):
    """计算相邻网格的geohash编码"""
    geohash = geohash.lower()
    last_char = geohash[-1]
    type_ = len(geohash) % 2
    dir_index = Direction(direction)
    nhash = geohash[:-1]

    if last_char in Borders[type_][dir_index]:
        nhash = calculate_adjacent(nhash, direction)

    new_last_char = BASE32[Neighbors[type_][dir_index].index(last_char)]
    return nhash + new_last_char


def altitude_encode(altitude):
    # Ensure altitude is non-negative before encoding
    safe_altitude = max(0, altitude)
    return str(safe_altitude).zfill(2)


class GeoHashHandler:
    def __init__(self):
        self.BASE32 = BASE32
        self.BITS = BITS

    def encode_coordinates(self, lat, lon, alt):
        """将经纬度和高度坐标编码为网格ID"""
        precision = 9  # 固定精度为9位
        geohash = ""
        even = True
        bit = 0
        ch = 0

        lat_range = [-90.0, 90.0]
        lon_range = [-180.0, 180.0]

        while len(geohash) < precision:
            mid = 0.0
            if even:
                mid = (lon_range[0] + lon_range[1]) / 2
                if lon > mid:
                    ch |= self.BITS[bit]
                    lon_range[0] = mid
                else:
                    lon_range[1] = mid
            else:
                mid = (lat_range[0] + lat_range[1]) / 2
                if lat > mid:
                    ch |= self.BITS[bit]
                    lat_range[0] = mid
                else:
                    lat_range[1] = mid

            even = not even
            if bit < 4:
                bit += 1
            else:
                geohash += self.BASE32[ch]
                bit = 0
                ch = 0

        # 添加高度编码（每5米一个单位）
        height_code = altitude_encode(int(alt / 5))  # Use altitude_encode helper
        # print(f"经纬度编码：{geohash}, 高度编码: {height_code}") # Removed print
        return geohash + height_code

    # encode_grid, _decimal_to_binary, _encode_base32 seem unused after refactoring,
    # but keep them for now in case GeoHashHandler is used elsewhere independently.
    def encode_grid(self, lat, lon, alt):
        """将经纬度和海拔编码为网格码"""
        try:
            lat_bin = self._decimal_to_binary(lat, 90)
            lon_bin = self._decimal_to_binary(lon, 180)

            geohash = ""
            interleaved_bin = "".join(l + b for l, b in zip(lon_bin, lat_bin))

            for i in range(0, len(interleaved_bin), 5):
                chunk = interleaved_bin[i : i + 5]
                if len(chunk) == 5:
                    geohash += self._encode_base32(chunk)

            # Ensure geohash has the correct precision (e.g., 9 for standard use)
            # This part might need adjustment based on how precision is defined/used.
            # Assuming precision 9 means 45 bits (9 * 5 bits/char)
            geohash = geohash[:9]  # Truncate or adjust as needed

            height = int(alt / 5)
            if height < 0 or height > 99:  # Standard range 0-99
                # Handle out-of-range altitude, maybe return None or clamp
                # For now, let's clamp to ensure a valid code is returned if possible
                height = max(0, min(99, height))
                logger.warning(
                    f"Altitude {alt}m is out of standard 0-495m range, clamped to {height*5}m for encoding."
                )
                # return None # Or return None if clamping is not desired

            return f"{geohash}{altitude_encode(height)}"
        except Exception as e:
            logger.error(f"编码网格时出错: {e}")
            return None

    def _decimal_to_binary(self, decimal, max_val):
        """将十进制数转换为二进制字符串 (for encode_grid)"""
        # Precision needs to match the desired geohash length * 2.5 bits
        # For precision 9 (45 bits total), we need 23 bits for lat and 22 for lon, or vice versa.
        # Let's aim for 25 bits each for simplicity, matching the original code.
        num_bits = 25
        binary = ""
        min_val = -max_val
        for _ in range(num_bits):
            mid = (min_val + max_val) / 2
            if decimal >= mid:
                binary += "1"
                min_val = mid
            else:
                binary += "0"
                max_val = mid
        return binary

    def _encode_base32(self, binary_chunk):
        """将5位二进制转换为base32字符"""
        if len(binary_chunk) != 5:
            raise ValueError("Binary chunk must be 5 bits long")
        index = int(binary_chunk, 2)
        return self.BASE32[index]

    def _refine_interval(self, interval, cd, mask):
        """细化区间范围"""
        mid = (interval[0] + interval[1]) / 2
        if cd & mask:
            interval[0] = mid
        else:
            interval[1] = mid

    def decode_grid(self, grid_id):
        """将网格ID解码为经纬度和高度坐标"""
        if not isinstance(grid_id, str) or len(grid_id) < 3:  # Basic validation
            logger.error(f"Invalid grid_id format for decoding: {grid_id}")
            return None
        if grid_id.endswith("--"):  # Handle invalid height code explicitly
            logger.warning(
                f"Grid ID {grid_id} has invalid height '--', cannot decode height."
            )
            # Optionally decode lat/lon only or return None
            # Let's return None as height is usually crucial
            return None

        geohash_part = grid_id[:-2]
        height_part = grid_id[-2:]

        even = True
        lat = [-90.0, 90.0]
        lon = [-180.0, 180.0]

        try:
            for char in geohash_part:
                cd = self.BASE32.index(char)
                for mask in self.BITS:
                    if even:
                        self._refine_interval(lon, cd, mask)
                    else:
                        self._refine_interval(lat, cd, mask)
                    even = not even

            latitude = (lat[0] + lat[1]) / 2.0
            longitude = (lon[0] + lon[1]) / 2.0

            # Decode height, handle potential errors
            height = int(height_part) * 5  # Each unit is 5 meters

            return {"lat": latitude, "lon": longitude, "alt": height}
        except (ValueError, IndexError) as e:
            logger.error(f"Error decoding grid_id '{grid_id}': {e}")
            return None

    def get_adjacent_grids(self, geohash_str):
        """获取相邻的网格"""
        if not isinstance(geohash_str, str) or len(geohash_str) < 3:
            logger.error(
                f"Invalid grid_id format for getting adjacent grids: {geohash_str}"
            )
            return []
        if geohash_str.endswith("--"):
            # Cannot calculate adjacent for invalid height
            return []

        geohash = geohash_str[:-2]
        height_code = geohash_str[-2:]
        try:
            height = int(height_code)  # Integer height index (0-99)
        except ValueError:
            logger.error(f"Invalid height code in grid_id: {geohash_str}")
            return []

        try:
            # Calculate neighbors in the same horizontal plane
            geohash_top = calculate_adjacent(geohash, "Top")
            geohash_bottom = calculate_adjacent(geohash, "Bottom")
            geohash_left = calculate_adjacent(geohash, "Left")
            geohash_right = calculate_adjacent(geohash, "Right")
            geohash_top_left = calculate_adjacent(geohash_left, "Top")
            geohash_top_right = calculate_adjacent(geohash_right, "Top")
            geohash_bottom_left = calculate_adjacent(geohash_left, "Bottom")
            geohash_bottom_right = calculate_adjacent(geohash_right, "Bottom")

            # Prepare lists for the three layers
            expand = []
            current_layer_hashes = [
                geohash_bottom,
                geohash_top,
                geohash_left,
                geohash_right,
                geohash_bottom_left,
                geohash_bottom_right,
                geohash_top_left,
                geohash_top_right,
            ]
            lower_layer_hashes = [
                geohash
            ] + current_layer_hashes  # Include center for lower/upper
            upper_layer_hashes = [
                geohash
            ] + current_layer_hashes  # Include center for lower/upper

            # Add height code for the current layer
            for h in current_layer_hashes:
                expand.append(h + height_code)

            # Add height code for the lower layer (height - 1)
            if height > 0:
                lower_height_code = altitude_encode(height - 1)
                for h in lower_layer_hashes:
                    expand.append(h + lower_height_code)
            else:
                # If current height is 0, lower layer is invalid ("--")
                # Depending on requirements, either skip or add with "--"
                # Skipping seems safer if "--" indicates invalidity
                pass
                # for h in lower_layer_hashes:
                #     expand.append(h + "--")

            # Add height code for the upper layer (height + 1)
            # Assuming a max height index (e.g., 99 for 0-495m range)
            max_height_index = 99  # Adjust if range is different
            if height < max_height_index:
                upper_height_code = altitude_encode(height + 1)
                for h in upper_layer_hashes:
                    expand.append(h + upper_height_code)
            else:
                # Handle reaching max height, similar to min height
                pass

            return expand
        except Exception as e:
            logger.error(f"Error calculating adjacent grids for '{geohash_str}': {e}")
            return []


class GridConverter:
    def __init__(self, map_config: MapConfig):
        """
        初始化网格转换器

        Args:
            map_config: 从 settings 加载的地图配置对象 (MapConfig)
        """
        if not isinstance(map_config, MapConfig):
            raise TypeError("map_config must be an instance of MapConfig")

        # 验证下采样比例
        if map_config.downsample_ratio <= 0:
            raise ValueError("下采样比例必须大于0")

        self.downsample_ratio = map_config.downsample_ratio
        logger.info(f"初始化GridConverter，下采样比例: {self.downsample_ratio}")

        self.geohash_handler = GeoHashHandler()

        # Directly use parameters from map_config
        self.min_coords = {
            "lat": map_config.min_coords.lat,
            "lon": map_config.min_coords.lon,
            "alt": map_config.min_coords.alt,
        }
        self.grid_size = {
            "lat": map_config.grid_size.lat,
            "lon": map_config.grid_size.lon,
            "alt": map_config.grid_size.alt,
        }
        # Precision might be needed by GeoHashHandler or other methods, store it if necessary
        self.precision = map_config.precision

        # Log the loaded parameters
        # logger.info(f"GridConverter initialized with:")
        # logger.info(f"  Min Coords: {self.min_coords}")
        # logger.info(f"  Grid Size: {self.grid_size}")
        # logger.info(f"  Precision: {self.precision}")

        # Database and mapping file related properties and methods are no longer needed.

    # Methods related to database fetching, mapping file I/O, and old coordinate systems
    # have been removed as the converter now relies solely on the provided map_config.

    def relative_to_geo(self, i: int, j: int, k: int) -> Dict[str, float]:
        """
        将相对坐标(i,j,k)直接转换为经纬度坐标，考虑纬度对经度的影响

        Args:
            i: 相对坐标i (南北方向, 通常对应纬度)
            j: 相对坐标j (东西方向, 通常对应经度)
            k: 相对高度 (上下方向)

        Returns:
            dict: {'lat': 纬度, 'lon': 经度, 'alt': 高度(米)}
        """
        # Ensure grid_size and min_coords are initialized (should be by __init__)
        if not self.grid_size or not self.min_coords:
            # This should ideally not happen if __init__ is called correctly
            raise RuntimeError("GridConverter not properly initialized.")

        # 应用上采样恢复原始坐标
        i_adj = i * self.downsample_ratio
        j_adj = j * self.downsample_ratio
        k_adj = k * self.downsample_ratio

        # 首先计算纬度（与原来相同）
        lat = self.min_coords["lat"] + (i_adj * self.grid_size["lat"])

        # 计算经度时考虑纬度的影响
        # 在不同纬度上，相同经度差对应的实际距离不同
        # 使用余弦函数来调整经度的比例
        # 纬度越高，经度间的实际距离越小
        # lat_radians = math.radians(lat)
        # lon_scale_factor = math.cos(lat_radians)  # 纬度的余弦值作为比例因子

        # # 应用比例因子到经度计算中
        # # 当纬度接近赤道时，lon_scale_factor接近1，经度计算几乎不变
        # # 当纬度接近极点时，lon_scale_factor接近0，经度变化更显著
        # lon = self.min_coords["lon"] + (j * self.grid_size["lon"] / lon_scale_factor)

        lon = self.min_coords["lon"] + (j_adj * self.grid_size["lon"])

        # 高度计算保持不变
        alt = self.min_coords["alt"] + (k_adj * self.grid_size["alt"])

        return {"lat": lat, "lon": lon, "alt": alt}

    def geo_to_relative(
        self, lat: float, lon: float, alt: float = None
    ) -> Tuple[int, int, int]:
        """
        将经纬度坐标直接转换为相对坐标

        Args:
            lat: 纬度
            lon: 经度
            alt: 高度(米), 可选

        Returns:
            tuple: (i, j, k) 相对坐标. 如果 alt is None, k is omitted.
                   Returns None if grid_size components are zero to avoid division by zero.
        """
        if not self.grid_size or not self.min_coords:
            raise RuntimeError("GridConverter not properly initialized.")

        # Check for zero division
        if self.grid_size["lat"] == 0 or self.grid_size["lon"] == 0:
            logger.error("Grid size for latitude or longitude is zero, cannot convert.")
            return None  # Or raise an error

        # 计算原始网格坐标
        i_raw = (lat - self.min_coords["lat"]) / self.grid_size["lat"]
        j_raw = (lon - self.min_coords["lon"]) / self.grid_size["lon"]

        # 应用下采样
        i = round(i_raw / self.downsample_ratio)
        j = round(j_raw / self.downsample_ratio)

        if alt is None:
            return (int(i), int(j))

        if self.grid_size["alt"] == 0:
            logger.error("Grid size for altitude is zero, cannot convert height.")
            return None  # Or raise an error

        # 计算原始高度坐标并应用下采样
        k_raw = (alt - self.min_coords["alt"]) / self.grid_size["alt"]
        k = round(k_raw / self.downsample_ratio)

        return (int(i), int(j), int(k))

    def height_to_relative(self, absolute_height: float) -> int:
        """
        将绝对高度（米）转换为相对高度（网格坐标k）

        Args:
            absolute_height: 绝对高度（米）

        Returns:
            int: 相对高度k（网格坐标）
                 Returns None if grid_size for altitude is zero.
        """
        if not self.grid_size or not self.min_coords:
            raise RuntimeError("GridConverter not properly initialized.")

        if self.grid_size["alt"] == 0:
            logger.error("Grid size for altitude is zero, cannot convert height.")
            return None  # Or raise an error

        base_height = self.min_coords.get("alt", 0)
        relative_k = round((absolute_height - base_height) / self.grid_size["alt"])

        return int(relative_k)  # Ensure integer return


# Example usage (if needed for testing, requires settings object)
# if __name__ == "__main__":
#     # This part needs adjustment as GridConverter now requires MapConfig
#     # You would typically get map_config from settings
#     try:
#         from ...config.settings import settings # Assuming settings is initialized
#         map_config = settings.map
#         converter = GridConverter(map_config)
#
#         # Test conversions
#         test_geo = {"lat": 31.95, "lon": 117.37, "alt": 50}
#         relative_coords = converter.geo_to_relative(test_geo["lat"], test_geo["lon"], test_geo["alt"])
#         print(f"Geo {test_geo} -> Relative: {relative_coords}")
#
#         if relative_coords:
#              retrieved_geo = converter.relative_to_geo(relative_coords[0], relative_coords[1], relative_coords[2])
#              print(f"Relative {relative_coords} -> Geo: {retrieved_geo}")
#
#              relative_k = converter.height_to_relative(test_geo["alt"])
#              print(f"Absolute height {test_geo['alt']}m -> Relative k: {relative_k}")
#
#     except ImportError:
#          print("Could not import settings for example usage.")
#     except Exception as e:
#          print(f"An error occurred during example usage: {e}")
